# -*- coding: utf-8 -*-
"""
[V-Final-Fix-11] 专业级手动批量投放工具 (配置中心适配版)
严格遵循主工作流的成功模式，确保数据隔离和会话安全。
从 config/settings.yml 读取业务参数。
"""

import argparse
import os
import sys
import random
import time
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Optional

# --- 路径设置 ---
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, project_root)
sys.path.insert(0, src_path)

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.config_manager import get_config_manager, load_settings
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.utils.exceptions import QianchuanDatabaseError, QianchuanAPIError
from qianchuan_aw.database.models import AdAccount, Principal, PlatformCreative
from qianchuan_aw.sdk_qc.client import QianchuanClient
from qianchuan_aw.workflows.common.plan_creation import create_ad_plan
from qianchuan_aw.utils.file_utils import get_file_md5
from qianchuan_aw.utils.workflow_helpers import (
    check_rate_limit,
    extract_cover_id_from_url,
    find_or_create_local_creative,
    get_random_value_from_range
)
from qianchuan_aw.utils.video_similarity import find_similar_videos_by_phash

VALID_VIDEO_EXTENSIONS = ['.mp4', '.mov', '.avi', '.mkv']

def group_videos_by_similarity(video_paths: List[str], creative_count_per_plan: int, config: Dict[str, any]) -> List[List[str]]:
    """
    根据相似度对视频进行智能分组

    Args:
        video_paths: 视频文件路径列表
        creative_count_per_plan: 每个计划的素材数量
        config: 相似度配置参数

    Returns:
        分组后的视频路径列表，每个子列表代表一个计划的素材组
    """
    if not video_paths:
        logger.info("视频列表为空，返回空分组")
        return []

    if len(video_paths) == 1:
        logger.info("只有一个视频，创建单个分组")
        return [video_paths]

    logger.info(f"开始相似度智能分组：{len(video_paths)} 个视频，每组 {creative_count_per_plan} 个素材")

    try:
        # 获取相似度配置
        similarity_config = config.get('video_similarity', {})

        # 使用相似度算法找出相似视频组
        similar_groups, ungrouped_videos = find_similar_videos_by_phash(video_paths, similarity_config)

        logger.info(f"相似度检测结果：{len(similar_groups)} 个相似组，{len(ungrouped_videos)} 个未分组视频")

        # 创建最终的计划分组
        final_groups = []
        remaining_videos = ungrouped_videos.copy()

        # 处理相似视频组
        for similar_group in similar_groups:
            # 如果相似组的视频数量正好等于计划容量，直接作为一个计划
            if len(similar_group) == creative_count_per_plan:
                final_groups.append(similar_group)
                logger.info(f"相似组正好匹配计划容量：{len(similar_group)} 个视频")

            # 如果相似组的视频数量小于计划容量，需要补充其他视频
            elif len(similar_group) < creative_count_per_plan:
                current_group = similar_group.copy()
                needed = creative_count_per_plan - len(current_group)

                # 从剩余视频中补充
                while needed > 0 and remaining_videos:
                    current_group.append(remaining_videos.pop(0))
                    needed -= 1

                if len(current_group) >= creative_count_per_plan or not remaining_videos:
                    final_groups.append(current_group)
                    logger.info(f"相似组补充后：{len(current_group)} 个视频")
                else:
                    # 如果补充后仍不足，将视频放回剩余列表
                    remaining_videos.extend(current_group)

            # 如果相似组的视频数量大于计划容量，需要拆分
            else:
                while len(similar_group) >= creative_count_per_plan:
                    final_groups.append(similar_group[:creative_count_per_plan])
                    similar_group = similar_group[creative_count_per_plan:]
                    logger.info(f"相似组拆分：创建 {creative_count_per_plan} 个视频的计划")

                # 剩余的视频加入待分组列表
                if similar_group:
                    remaining_videos.extend(similar_group)

        # 处理剩余的未分组视频
        while len(remaining_videos) >= creative_count_per_plan:
            final_groups.append(remaining_videos[:creative_count_per_plan])
            remaining_videos = remaining_videos[creative_count_per_plan:]
            logger.info(f"常规分组：创建 {creative_count_per_plan} 个视频的计划")

        # 处理最后剩余的视频
        if remaining_videos:
            if final_groups and len(remaining_videos) < creative_count_per_plan:
                # 如果剩余视频不足一个计划，合并到最后一个计划中
                final_groups[-1].extend(remaining_videos)
                logger.info(f"剩余视频合并到最后一个计划：{len(remaining_videos)} 个视频")
            else:
                # 如果没有其他计划或剩余视频足够，创建新计划
                final_groups.append(remaining_videos)
                logger.info(f"剩余视频创建新计划：{len(remaining_videos)} 个视频")

        logger.info(f"相似度分组完成：共创建 {len(final_groups)} 个计划")
        return final_groups

    except Exception as e:
        logger.error(f"相似度分组失败，回退到常规分组：{e}")
        # 回退到常规分组
        regular_groups = []
        for i in range(0, len(video_paths), creative_count_per_plan):
            regular_groups.append(video_paths[i:i + creative_count_per_plan])
        return regular_groups

def get_video_files(folder_path: str) -> List[str]:
    """扫描文件夹并返回所有有效视频文件的完整路径列表。"""
    video_files = []
    logger.info(f"开始扫描文件夹: {folder_path}")
    for filename in os.listdir(folder_path):
        if any(filename.lower().endswith(ext) for ext in VALID_VIDEO_EXTENSIONS):
            full_path = os.path.join(folder_path, filename)
            video_files.append(full_path)
    logger.info(f"扫描完成，共找到 {len(video_files)} 个视频文件。")
    return video_files

def upload_and_register_task(client: QianchuanClient, principal_id: int, account_id: int, video_path: str) -> Optional[int]:
    """
    增强版上传和注册任务，内置实时监控和重试机制
    返回 platform_creative_id，如果失败返回 None
    """

    def classify_error(error_message: str) -> bool:
        """分类错误是否可重试"""
        error_lower = error_message.lower()

        # 不可重试的错误
        permanent_errors = [
            'file not found', '文件不存在', '文件格式不支持',
            'invalid format', '格式错误', '文件损坏'
        ]

        for error in permanent_errors:
            if error in error_lower:
                return False

        # 默认认为可重试
        return True

    def verify_upload_success(video_id: str, expected_filename: str) -> bool:
        """验证上传是否真正成功"""
        try:
            logger.info(f"🔍 验证上传结果: video_id={video_id}")

            library_result = client.get_library_videos(
                advertiser_id=int(account.account_id_qc),
                filtering={"video_ids": [video_id]}
            )

            if not library_result or 'list' not in library_result:
                logger.error("❌ 反查素材库失败：返回结果为空")
                return False

            videos = library_result['list']
            if not videos:
                logger.error("❌ 反查素材库失败：视频列表为空")
                return False

            video_info = videos[0]
            actual_filename = video_info.get('filename', '')

            if actual_filename != expected_filename:
                logger.error(f"❌ 文件名不匹配：期望={expected_filename}, 实际={actual_filename}")
                return False

            logger.success(f"✅ 上传验证成功：{expected_filename}")
            return True

        except Exception as e:
            logger.error(f"❌ 验证过程异常：{e}")
            return False

    # 主要逻辑
    max_retries = 3
    retry_delays = [10, 30, 60]  # 10秒, 30秒, 60秒
    filename = os.path.basename(video_path)

    logger.info(f"🎬 开始处理视频：{filename}")
    logger.info("=" * 60)

    # 文件检查和数据库准备
    try:
        with database_session() as db:
            try:
                file_hash = get_file_md5(video_path)
                logger.info(f"📋 文件哈希：{file_hash}")
            except (FileNotFoundError, IOError, ValueError) as e:
                logger.error(f"❌ 无法计算文件哈希值: {video_path}, 错误: {e}")
                return None

            local_creative = find_or_create_local_creative(db, principal_id, file_hash, video_path)
            account = db.get(AdAccount, account_id)
            if not account:
                logger.error(f"❌ 数据库中找不到 account_id={account_id}")
                return None

            existing_pc = db.query(PlatformCreative).filter_by(local_creative_id=local_creative.id, account_id=account.id).first()
            if existing_pc:
                logger.info(f"✅ 素材已存在，跳过上传：ID={existing_pc.id}")
                return existing_pc.id

            # 执行上传（带重试机制）
            for attempt in range(1, max_retries + 1):
                logger.info(f"📤 第 {attempt} 次上传尝试...")

                try:
                    # 执行上传
                    start_time = time.time()
                    video_info = client.upload_video(
                        advertiser_id=int(account.account_id_qc),
                        video_file_path=video_path,
                        video_signature=file_hash
                    )
                    upload_duration = time.time() - start_time

                    # 检查上传结果
                    if not video_info or 'video_id' not in video_info:
                        error_msg = f"上传结果异常：{video_info}"
                        logger.error(f"❌ {error_msg}")

                        if attempt < max_retries and classify_error(error_msg):
                            delay = retry_delays[min(attempt-1, len(retry_delays)-1)]
                            logger.warning(f"⏰ {delay}秒后重试...")
                            time.sleep(delay)
                            continue
                        else:
                            logger.error(f"❌ 上传永久失败")
                            return None

                    video_id = video_info.get("video_id")
                    logger.success(f"✅ 上传成功！video_id={video_id} (耗时: {upload_duration:.2f}秒)")

                    # 等待平台处理
                    time.sleep(5)

                    # 验证上传结果
                    if not verify_upload_success(video_id, filename):
                        if attempt < max_retries:
                            delay = retry_delays[min(attempt-1, len(retry_delays)-1)]
                            logger.warning(f"⏰ 验证失败，{delay}秒后重试整个上传过程...")
                            time.sleep(delay)
                            continue
                        else:
                            logger.error(f"❌ 验证永久失败")
                            return None

                    # 获取详细信息
                    try:
                        logger.info("📋 获取素材详细信息...")
                        library_videos_response = client.get_library_videos(
                            advertiser_id=int(account.account_id_qc),
                            filtering={"video_ids": [video_id]}
                        )

                        if not library_videos_response or 'list' not in library_videos_response:
                            raise Exception("反查素材库失败")

                        library_videos = library_videos_response.get('list', [])
                        if not library_videos:
                            raise Exception("反查素材库失败，list 为空")

                        library_video_data = library_videos[0]
                        poster_url = library_video_data.get("poster_url")
                        video_cover_id = extract_cover_id_from_url(poster_url)

                        if not video_cover_id:
                            raise Exception(f"无法从 poster_url '{poster_url}' 中提取封面ID")

                        material_id_str = str(video_info.get('material_id'))
                        logger.info(f"📋 素材信息：material_id={material_id_str}")

                    except Exception as e:
                        error_msg = f"获取素材信息失败：{e}"
                        logger.error(f"❌ {error_msg}")

                        if attempt < max_retries:
                            delay = retry_delays[min(attempt-1, len(retry_delays)-1)]
                            logger.warning(f"⏰ {delay}秒后重试...")
                            time.sleep(delay)
                            continue
                        else:
                            logger.error(f"❌ 获取素材信息永久失败")
                            return None

                    # 创建数据库记录
                    try:
                        logger.info("💾 创建数据库记录...")

                        platform_creative = PlatformCreative(
                            local_creative_id=local_creative.id,
                            account_id=account.id,
                            material_id_qc=material_id_str,
                            video_id=video_id,
                            video_url=library_video_data.get("url"),
                            video_cover_id=video_cover_id,
                            review_status='pending',
                            promotion_status='launched'
                        )

                        db.add(platform_creative)
                        db.commit()
                        db.refresh(platform_creative)

                        platform_creative_id = platform_creative.id
                        logger.success(f"✅ 数据库记录创建成功：ID={platform_creative_id}")

                        # 最终验证
                        logger.info("🔍 最终验证...")
                        final_check = db.query(PlatformCreative).filter(
                            PlatformCreative.id == platform_creative_id
                        ).first()

                        if not final_check:
                            raise Exception("数据库记录验证失败")

                        logger.success(f"🎉 视频处理完全成功：{filename}")
                        logger.info("=" * 60)
                        return platform_creative_id

                    except Exception as e:
                        error_msg = f"数据库操作失败：{e}"
                        logger.error(f"❌ {error_msg}")

                        if attempt < max_retries:
                            delay = retry_delays[min(attempt-1, len(retry_delays)-1)]
                            logger.warning(f"⏰ {delay}秒后重试...")
                            time.sleep(delay)
                            continue
                        else:
                            logger.error(f"❌ 数据库操作永久失败")
                            return None

                except Exception as e:
                    error_msg = f"上传过程异常：{e}"
                    logger.error(f"❌ {error_msg}")

                    if attempt < max_retries and classify_error(str(e)):
                        delay = retry_delays[min(attempt-1, len(retry_delays)-1)]
                        logger.warning(f"⏰ {delay}秒后重试...")
                        time.sleep(delay)
                        continue
                    else:
                        logger.error(f"❌ 上传永久失败")
                        return None

            # 如果所有重试都失败了
            logger.error(f"❌ 所有重试尝试都失败了：{filename}")
            logger.info("=" * 60)
            return None

    except Exception as e:
        logger.error(f"❌ 处理视频上传注册任务时出错: {video_path}, 错误: {e}")
        logger.info("=" * 60)
        return None

def process_videos(videos: List[str], principal: Principal, account: AdAccount, args: argparse.Namespace):
    """根据策略处理所有视频的上传和计划创建。"""
    if not videos:
        return
        
    logger.info(f"--- 开始处理 {len(videos)} 个视频，策略: '{args.strategy}' ---")

    config_manager = get_config_manager()
    app_settings = config_manager.get_config()
    api_credentials = config_manager.get_api_credentials()
    app_id = api_credentials['app_id']
    secret = api_credentials['secret']

    client = QianchuanClient(app_id=app_id, secret=secret, principal_id=principal.id)
    if not client.is_token_valid():
        logger.error(f"账户 {account.name} 的Token无效，无法处理。")
        return

    pc_ids = []
    with ThreadPoolExecutor(max_workers=app_settings['workflow']['max_upload_workers']) as executor:
        future_to_video = {executor.submit(upload_and_register_task, client, principal.id, account.id, video_path): video_path for video_path in videos}
        for future in as_completed(future_to_video):
            result_id = future.result()
            if result_id:
                pc_ids.append(result_id)

    if not pc_ids:
        logger.error(f"所有视频均处理失败，无法创建计划。")
        return

    with database_session() as db:
        uploaded_creatives = db.query(PlatformCreative).filter(PlatformCreative.id.in_(pc_ids)).all()

        manual_workflow_config = app_settings['plan_creation_defaults'].get('manual_workflow', {})
        creative_count_per_plan = manual_workflow_config.get('creative_count', 3)

        # 检查是否启用相似度分组
        if hasattr(args, 'use_similarity_grouping') and args.use_similarity_grouping:
            logger.info("启用相似度智能分组")

            # 获取视频路径列表（从PlatformCreative对象中提取）
            video_paths = []
            creative_path_map = {}

            for creative in uploaded_creatives:
                # 通过local_creative获取原始文件路径
                local_creative = creative.local_creative
                if local_creative and local_creative.file_path:
                    file_path = local_creative.file_path

                    # 检查文件是否存在，如果不存在则尝试修复路径
                    if not os.path.exists(file_path):
                        logger.warning(f"文件不存在: {file_path}")

                        # 尝试多种路径修复策略
                        filename = os.path.basename(file_path)
                        possible_paths = []

                        # 策略1: 使用args.path + 文件名
                        if hasattr(args, 'path') and args.path:
                            possible_paths.append(os.path.join(args.path, filename))

                        # 策略2: 使用local_creative.filename
                        if local_creative.filename and hasattr(args, 'path') and args.path:
                            possible_paths.append(os.path.join(args.path, local_creative.filename))

                        # 策略3: 修复数据库路径中的目录结构问题
                        if '缇萃百货\\2025-07-30\\' in file_path and '\\上1\\' not in file_path:
                            # 在2025-07-30后面插入\上1
                            fixed_path = file_path.replace('缇萃百货\\2025-07-30\\', '缇萃百货\\2025-07-30\\上1\\')
                            possible_paths.append(fixed_path)

                        # 尝试所有可能的路径
                        for test_path in possible_paths:
                            if test_path and os.path.exists(test_path):
                                logger.info(f"找到修复路径: {test_path}")
                                file_path = test_path
                                break
                        else:
                            logger.error(f"所有路径修复策略都失败: {filename}")
                            continue  # 跳过这个文件

                    video_paths.append(file_path)
                    creative_path_map[file_path] = creative

            if video_paths:
                # 使用相似度分组
                grouped_paths = group_videos_by_similarity(video_paths, creative_count_per_plan, app_settings)

                # 将路径分组转换为PlatformCreative分组
                creative_chunks = []
                for path_group in grouped_paths:
                    creative_group = []
                    for path in path_group:
                        if path in creative_path_map:
                            creative_group.append(creative_path_map[path])
                    if creative_group:
                        creative_chunks.append(creative_group)

                if len(creative_chunks) > 0:
                    avg_per_group = sum(len(c) for c in creative_chunks) / len(creative_chunks)
                    logger.info(f"相似度分组结果: {len(creative_chunks)} 个组，平均每组 {avg_per_group:.1f} 个素材")
                else:
                    logger.warning("相似度分组结果: 0 个组，所有视频都无法处理")
            else:
                logger.warning("无法获取视频路径，回退到常规分组")
                creative_chunks = [uploaded_creatives[i:i + creative_count_per_plan] for i in range(0, len(uploaded_creatives), creative_count_per_plan)]
        else:
            # 使用常规分组
            creative_chunks = [uploaded_creatives[i:i + creative_count_per_plan] for i in range(0, len(uploaded_creatives), creative_count_per_plan)]
            logger.info(f"成功处理 {len(uploaded_creatives)} 个素材，将被分为 {len(creative_chunks)} 个计划进行创建。")

        for chunk in creative_chunks:
            if not check_rate_limit(account.id, app_settings['workflow']['hourly_plan_creation_rate_limit'], app_settings):
                logger.warning("由于达到速率限制，剩余的计划将不会被创建。")
                break
            
            strategy_map = manual_workflow_config.get('strategies', {})
            strategy_config = strategy_map.get(args.strategy)
            
            if not strategy_config:
                logger.error(f"未知的策略: {args.strategy}。请检查 config/settings.yml 文件。")
                continue

            budget = args.budget if args.budget is not None else get_random_value_from_range(manual_workflow_config.get('default_budget_range'), precision=0)
            if budget is None:
                logger.error("无法生成有效预算，跳过本轮创建。")
                continue

            cpa_bid = None
            roi_goal = None
            
            bid_type = strategy_config.get('bid')
            if bid_type == 'DEAL':
                cpa_bid = args.cpa if args.cpa is not None else get_random_value_from_range(manual_workflow_config.get('default_cpa_bid_range'), precision=2)
                if cpa_bid is None:
                    logger.error("无法为 'DEAL' 策略生成有效的 CPA 出价，跳过本轮创建。")
                    continue
            elif bid_type == 'ROI':
                roi_goal = args.roi if args.roi is not None else get_random_value_from_range(manual_workflow_config.get('default_roi_goal_range'), precision=2)
                if roi_goal is None:
                    logger.error("无法为 'ROI' 策略生成有效的 ROI 目标，跳过本轮创建。")
                    continue
            
            logger.info(f"为本次计划创建动态生成的参数 -> 预算: {budget}, CPA出价: {cpa_bid}, ROI目标: {roi_goal}")

            create_ad_plan(
                db=db, principal=principal, account=account,
                platform_creatives=chunk,
                campaign_scene=strategy_config['scene'],
                bid_type=strategy_config['bid'],
                is_lab_ad=strategy_config['lab'],
                budget=budget,
                cpa_bid=cpa_bid,
                roi_goal=roi_goal,
                app_settings=app_settings
            )
            # 使用自定义间隔或默认配置
            interval = args.plan_creation_interval if hasattr(args, 'plan_creation_interval') and args.plan_creation_interval else app_settings['robustness']['plan_creation_interval']
            logger.info(f"手动计划创建完毕，等待 {interval} 秒...")
            time.sleep(interval)

def analyze_directory_for_grouping(directory_path: str, creative_count_per_plan: int = 3):
    """预分析目录，制定分组计划"""
    logger.info(f"🔍 预分析目录: {directory_path}")

    # 获取所有视频文件
    video_files = []
    if os.path.exists(directory_path):
        for filename in os.listdir(directory_path):
            if any(filename.lower().endswith(ext) for ext in VALID_VIDEO_EXTENSIONS):
                full_path = os.path.join(directory_path, filename)
                if os.path.isfile(full_path):
                    video_files.append(full_path)

    logger.info(f"找到 {len(video_files)} 个视频文件")

    if not video_files:
        return []

    # 基于文件名的智能分组
    name_groups = {}
    for video_path in video_files:
        filename = os.path.basename(video_path)
        if '-' in filename:
            parts = filename.split('-')
            if len(parts) >= 2:
                actor = parts[1] if len(parts) > 1 else parts[0]
                content_id = parts[2] if len(parts) > 2 else ""
                group_key = f"{actor}-{content_id.split('.')[0]}"

                if group_key not in name_groups:
                    name_groups[group_key] = []
                name_groups[group_key].append(video_path)

    # 创建最优分组
    final_groups = []
    remaining_videos = []

    # 处理基于名称的分组
    for group_name, videos in name_groups.items():
        if len(videos) >= creative_count_per_plan:
            while len(videos) >= creative_count_per_plan:
                final_groups.append({
                    'type': 'name_based',
                    'description': f'{group_name} 系列',
                    'videos': videos[:creative_count_per_plan]
                })
                videos = videos[creative_count_per_plan:]
            if videos:
                remaining_videos.extend(videos)
        else:
            remaining_videos.extend(videos)

    # 处理剩余视频
    while len(remaining_videos) >= creative_count_per_plan:
        final_groups.append({
            'type': 'mixed',
            'description': '混合组',
            'videos': remaining_videos[:creative_count_per_plan]
        })
        remaining_videos = remaining_videos[creative_count_per_plan:]

    # 最后剩余的视频合并到最后一个组
    if remaining_videos and final_groups:
        final_groups[-1]['videos'].extend(remaining_videos)
        final_groups[-1]['description'] += f' (+{len(remaining_videos)}个补充)'
    elif remaining_videos:
        final_groups.append({
            'type': 'remainder',
            'description': '剩余组',
            'videos': remaining_videos
        })

    logger.info(f"📋 预分析完成: {len(final_groups)} 个计划组，总计 {sum(len(g['videos']) for g in final_groups)} 个视频")

    return final_groups

def run_manual_launch(path: str, advertiser_id: int, strategy: str, budget: Optional[float], cpa: Optional[float], roi: Optional[float],
                     use_similarity_grouping: bool = False, plan_creation_interval: Optional[int] = None):
    """
    可被外部调用的手动投放核心逻辑。
    """
    class Args:
        def __init__(self):
            self.path = path
            self.advertiser_id = advertiser_id
            self.strategy = strategy
            self.budget = budget
            self.cpa = cpa
            self.roi = roi
            self.use_similarity_grouping = use_similarity_grouping
            self.plan_creation_interval = plan_creation_interval
    
    args = Args()

    logger.info("--- [专业级手动批量投放] 任务启动 ---")

    if not os.path.isdir(args.path):
        logger.error(f"错误：提供的路径 '{args.path}' 不是一个有效的文件夹。")
        raise FileNotFoundError(f"路径不存在或不是文件夹: {args.path}")

    with database_session() as db:
        account = db.query(AdAccount).filter(AdAccount.account_id_qc == str(args.advertiser_id)).first()
        if not account:
            msg = f"错误：在数据库中找不到广告账户ID为 '{args.advertiser_id}' 的记录。"
            logger.error(msg)
            raise ValueError(msg)
        
        principal = account.principal
        if not principal:
            msg = f"数据库健全性错误：账户 '{account.name}' 没有关联到任何主体。"
            logger.error(msg)
            raise ValueError(msg)
            
        logger.info(f"成功关联到账户: {account.name} (主体: {principal.name})")

        video_files = get_video_files(args.path)
        if not video_files:
            logger.warning("文件夹中没有找到任何视频文件，任务结束。")
            return

        # [V59] 支持简化的策略模式系统
        if args.strategy == 'smart_mix':
            logger.info("启用智能组合策略模式")

            # 使用简化的策略配置系统
            try:
                from qianchuan_aw.utils.simple_strategy_config import simple_config

                # 获取当前激活的策略配置
                current_strategies = simple_config.get_current_strategies()
                active_config = simple_config.get_active_config()

                if not current_strategies:
                    logger.error("当前没有激活的策略配置")
                    return

                # 验证配置
                errors = simple_config.validate_strategies(current_strategies)
                if errors:
                    logger.error(f"策略配置有误: {', '.join(errors)}")
                    return

                logger.info(f"使用策略模式: {active_config}")
                logger.info(f"策略比例: {current_strategies}")

                # 打乱视频顺序确保随机性
                random.shuffle(video_files)
                total_videos = len(video_files)

                # 按比例分配视频
                processed_count = 0

                for strategy_name, ratio in current_strategies.items():
                    if ratio <= 0:
                        continue

                    # 计算当前策略应分配的视频数量
                    num_videos = int(total_videos * ratio)

                    # 获取视频片段
                    start_idx = processed_count
                    end_idx = min(processed_count + num_videos, total_videos)
                    strategy_videos = video_files[start_idx:end_idx]

                    if strategy_videos:
                        logger.info(f"--- 策略模式: 处理 {len(strategy_videos)} 个视频为 '{strategy_name}' (比例: {ratio*100:.1f}%) ---")

                        # 临时修改策略参数
                        original_strategy = args.strategy
                        args.strategy = strategy_name

                        try:
                            process_videos(strategy_videos, principal, account, args)
                        finally:
                            args.strategy = original_strategy

                        processed_count = end_idx

                # 处理剩余视频（由于整数除法可能产生的余数）
                if processed_count < total_videos:
                    remaining_videos = video_files[processed_count:]
                    if remaining_videos:
                        # 使用比例最大的策略处理剩余视频
                        max_ratio_strategy = max(current_strategies.items(), key=lambda x: x[1])[0]
                        logger.info(f"--- 策略模式: 处理剩余 {len(remaining_videos)} 个视频为 '{max_ratio_strategy}' ---")

                        args.strategy = max_ratio_strategy
                        process_videos(remaining_videos, principal, account, args)

            except Exception as e:
                logger.error(f"智能组合策略执行失败: {e}")
                logger.info("回退到单一策略模式")
                # 回退到默认策略
                args.strategy = 'custom_deal'
                process_videos(video_files, principal, account, args)
        else:
            # 单一策略处理
            if args.use_similarity_grouping:
                # 使用预分析工作流程
                logger.info("=" * 60)
                logger.info("🎯 启用预分析相似度分组工作流程")
                logger.info("=" * 60)

                # 1. 预分析目录，制定分组计划
                grouping_plan = analyze_directory_for_grouping(args.path, creative_count_per_plan=3)
                if not grouping_plan:
                    logger.error("预分析失败，回退到常规流程")
                    process_videos(video_files, principal, account, args)
                else:
                    logger.info(f"📋 分组计划: {len(grouping_plan)} 个计划组")

                    # 2. 按计划逐个执行
                    for i, group_plan in enumerate(grouping_plan, 1):
                        logger.info(f"\n🎬 执行计划 {i}/{len(grouping_plan)}: {group_plan['description']}")
                        logger.info(f"包含 {len(group_plan['videos'])} 个视频")

                        # 为这个组处理视频（不使用相似度分组，因为已经预分组了）
                        temp_args = Args()
                        temp_args.path = args.path
                        temp_args.advertiser_id = args.advertiser_id
                        temp_args.strategy = args.strategy
                        temp_args.budget = args.budget
                        temp_args.cpa = args.cpa
                        temp_args.roi = args.roi
                        temp_args.use_similarity_grouping = False  # 已经预分组了
                        temp_args.plan_creation_interval = args.plan_creation_interval

                        process_videos(group_plan['videos'], principal, account, temp_args)

                        # 计划创建间隔
                        if args.plan_creation_interval and i < len(grouping_plan):
                            logger.info(f"等待 {args.plan_creation_interval} 秒后创建下一个计划...")
                            time.sleep(args.plan_creation_interval)

                    logger.info("🎉 预分析分组工作流程完成！")
            else:
                # 常规工作流程
                process_videos(video_files, principal, account, args)


    logger.info("--- [专业级手动批量投放] 任务执行完毕 ---")

def main_cli():
    """命令行接口"""
    parser = argparse.ArgumentParser(
        description="专业级手动批量投放工具。通过命令行参数精确控制投放策略和出价。",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument("-p", "--path", type=str, required=True, help="包含视频素材的文件夹路径。")
    parser.add_argument("-a", "--advertiser_id", type=int, required=True, help="目标千川广告账户的ID。")
    
    # [V59] 简化的策略选择系统
    strategy_choices = [
        'custom_deal', 'custom_roi', 'managed_deal', 'managed_roi',
        'new_customer_deal', 'new_customer_roi', 'smart_mix'
    ]

    help_text = """投放策略:
- custom_deal: 自定义成交 - 自定义投放，以成交为优化目标
- custom_roi: 自定义ROI - 自定义投放，以ROI为优化目标
- managed_deal: 托管成交 - 托管投放，以成交为优化目标
- managed_roi: 托管ROI - 托管投放，以ROI为优化目标
- new_customer_deal: 新客成交 - 专门用于拉新的成交优化
- new_customer_roi: 新客ROI - 专门用于拉新的ROI优化
- smart_mix: 智能组合 (使用当前激活的策略模式)
"""

    parser.add_argument(
        "-s", "--strategy",
        type=str,
        required=True,
        choices=strategy_choices,
        help=help_text
    )
    
    app_settings = load_settings()
    manual_config = app_settings['plan_creation_defaults'].get('manual_workflow', {})
    
    budget_range_str = manual_config.get('default_budget_range', '[N/A]')
    cpa_range_str = manual_config.get('default_cpa_bid_range', '[N/A]')
    roi_range_str = manual_config.get('default_roi_goal_range', '[N/A]')

    parser.add_argument("--budget", type=float, help=f"计划的日预算。如果未提供，则从范围 {budget_range_str} 中随机生成。")
    parser.add_argument("--cpa", type=float, help=f"CPA出价 (用于 'DEAL' 类策略)。如果未提供，则从范围 {cpa_range_str} 中随机生成。")
    parser.add_argument("--roi", type=float, help=f"ROI目标 (用于 'ROI' 类策略)。如果未提供，则从范围 {roi_range_str} 中随机生成。")
    
    args = parser.parse_args()
    run_manual_launch(args.path, args.advertiser_id, args.strategy, args.budget, args.cpa, args.roi)

if __name__ == '__main__':
    main_cli()
