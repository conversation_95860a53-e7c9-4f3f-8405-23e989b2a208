import sys
import os
import streamlit as st
import pandas as pd
import time
from datetime import datetime, timedelta
from urllib.parse import urlparse, parse_qs
from typing import Tuple, List, Dict

# 必须在所有其他Streamlit命令之前设置页面配置
st.set_page_config(
    page_title="千川自动化管理系统",
    page_icon="🎯",
    layout="wide",
    initial_sidebar_state="expanded"
)

# --- 标准的 sys.path 设置 ---
project_root = os.path.dirname(os.path.abspath(__file__))
src_path = os.path.join(project_root, 'src')
ai_tools_path = os.path.join(project_root, 'ai_tools')
sys.path.insert(0, project_root)
sys.path.insert(0, src_path)
sys.path.insert(0, ai_tools_path)
# ---

# --- 后端模块导入 ---
from qianchuan_aw.database.database import SessionLocal
from qianchuan_aw.database.models import AdAccount, Principal, LocalCreative, Campaign
from qianchuan_aw.sdk_qc.client import QianchuanClient
from qianchuan_aw.utils.config_loader import load_settings
from qianchuan_aw.utils.logger import logger as backend_logger
from qianchuan_aw.utils.db_utils import database_session

# --- 统一账户选择组件 ---
sys.path.insert(0, os.path.join(project_root, 'ai_tools', 'maintenance'))
from ai_tool_20250718_maintenance_unified_account_selector import (
    create_multi_account_selector,  # 仅保留多选，用于特殊场景如抖音号授权
    get_account_options_dict
)
# --- 全局账户选择组件 ---
from ai_tool_20250718_maintenance_global_account_selector import (
    render_global_account_selector,
    render_global_favorite_manager,
    get_global_selected_account,
    get_current_account_info,
    require_account_selection
)


# --- 增强功能模块导入 ---
try:
    from ai_tool_20250720_system_monitoring_page import render_system_monitoring_page
    from ai_tool_20250720_enhanced_material_analytics import render_enhanced_material_analytics_page
    from ai_tool_20250720_complete_material_analytics import render_complete_material_analytics_page
    ENHANCED_FEATURES_AVAILABLE = True
except ImportError as e:
    print(f"警告: 增强功能模块导入失败: {e}")
    ENHANCED_FEATURES_AVAILABLE = False

    # 提供备用函数
    def render_system_monitoring_page():
        st.error("❌ 系统监控功能暂时不可用，请检查模块安装")

    def render_enhanced_material_analytics_page():
        st.error("❌ 增强版报表功能暂时不可用，请使用旧版报表")

    def render_complete_material_analytics_page():
        st.error("❌ 完整版报表功能暂时不可用，请使用旧版报表")

# --- 其他模块导入 ---
from sqlalchemy import select, func, or_
from sqlalchemy.orm import joinedload
from tools import snapshot as snapshot_tool
from tools import sync_advertisers as sync_tool
from tools import add_principal as add_principal_tool
from tools import manual_launch as manual_launch_tool
from tools import replicate_plan as replicate_plan_tool
from tools import harvest_and_relaunch as harvest_tool
from tools import add_banned_terms as banned_terms_tool

# --- 素材搜索页面导入 ---
from qianchuan_aw.ui.pages.material_search import show_material_search_page

# 素材状态中文翻译映射
STATUS_TRANSLATIONS = {
    'pending_grouping': '待分组',
    'processing': '处理中',
    'upload_failed': '上传失败',
    'uploaded_pending_plan': '已上传待创建计划',
    'creating_plan': '创建计划中',
    'testing_pending_review': '测试待审核',
    'approved': '审核通过',
    'rejected': '审核拒绝',
    'already_tested': '已测试',
    'in_production': '生产中',
    'archived': '已归档',
    'error': '错误状态'
}

def translate_status(status):
    """翻译素材状态为中文"""
    return STATUS_TRANSLATIONS.get(status, status)

from tools import sync_reports as sync_reports_tool
from src.qianchuan_aw.ui.pages.failed_materials_manager import show_failed_materials_manager

# --- 日志捕获 (适配 Loguru) ---
def run_with_log_capture(func, *args, **kwargs):
    # 使用字符串路径而不是函数作为 sink
    import tempfile
    import os

    # 创建临时文件来捕获日志
    temp_log_file = tempfile.NamedTemporaryFile(mode='w+', delete=False, suffix='.log')
    temp_log_file.close()

    handler_id = backend_logger.add(temp_log_file.name, format="{message}")

    try:
        func(*args, **kwargs)
        st.success("任务执行完毕！")
    except Exception as e:
        st.error(f"任务执行失败: {e}")
    finally:
        backend_logger.remove(handler_id)

        # 读取临时文件内容
        try:
            with open(temp_log_file.name, 'r', encoding='utf-8') as f:
                log_output = f.read()
            os.unlink(temp_log_file.name)  # 删除临时文件
        except Exception:
            log_output = "无法读取日志输出"

        st.code(log_output, language='log')

# --- 后端函数 ---
@st.cache_data(ttl=60)
def get_all_principals() -> list[str]:
    with SessionLocal() as db:
        return list(db.scalars(select(Principal.name).order_by(Principal.id)).all())

@st.cache_data(ttl=10)
def get_all_ad_accounts_df() -> pd.DataFrame:
    with SessionLocal() as db:
        # 使用 joinedload 预加载关联的 principal 数据，避免 N+1 查询
        accounts = db.query(AdAccount).options(joinedload(AdAccount.principal)).order_by(AdAccount.id).all()
        data = [
            {
                'ID': acc.id,
                '主体ID': acc.principal_id,
                '账户名称': acc.name,
                '千川ID': acc.account_id_qc,
                '类型': acc.account_type,
                '状态': acc.status,
                '抖音号UID': acc.aweme_id,
                '测试账户': acc.is_test_account
            } for acc in accounts
        ]
    return pd.DataFrame(data)

def update_single_account(account_id: int, updates: dict):
    with SessionLocal() as db:
        try:
            account = db.get(AdAccount, account_id)
            if account:
                for key, value in updates.items():
                    setattr(account, key, value)
                db.commit()
        except Exception as e:
            db.rollback()
            st.error(f"更新账户 {account_id} 失败: {e}")

@st.cache_data(ttl=10)
def get_all_snapshots() -> list[dict]:
    return snapshot_tool.list_snapshots_data()

# --- UI 渲染函数 ---

def render_accounts_page():
    st.header("广告账户管理")
    st.markdown("在此页面，您可以查看所有广告账户的详细信息，从千川服务器同步最新数据，并直接在表格中对账户信息进行修改。")
    
    principals = get_all_principals()
    if not principals:
        st.warning("数据库中还没有任何主体，请先在“系统设置”页面添加。")
        return

    with st.container():
        selected_principal = st.selectbox("选择主体以同步账户", options=principals, key="acc_principal_select")
        if st.button("🔄 同步广告主", use_container_width=True):
            if selected_principal:  # 确保选择了主体
                with st.spinner(f"正在为 '{selected_principal}' 同步广告主..."):
                    try:
                        sync_tool.sync_advertisers(selected_principal)
                        st.success("同步完成！")
                        st.cache_data.clear()
                        st.rerun()
                    except Exception as e:
                        st.error(f"同步失败: {e}")
            else:
                st.error("请先选择一个主体")
    
    st.info("💡 您可以直接在下表中修改“账户名称”、“类型”、“状态”、“抖音号UID”和“测试账户”字段，修改后会自动保存。")
    
    accounts_df = get_all_ad_accounts_df()
    
    edited_df = st.data_editor(
        accounts_df,
        column_config={
            "ID": st.column_config.NumberColumn(disabled=True),
            "主体ID": st.column_config.NumberColumn(disabled=True),
            "千川ID": st.column_config.TextColumn(disabled=True),
            "类型": st.column_config.SelectboxColumn("类型", options=['UNSET', 'TEST', 'DELIVERY']),
            "状态": st.column_config.SelectboxColumn("状态", options=['active', 'disabled', 'temporarily_blocked']),
            "测试账户": st.column_config.CheckboxColumn("测试账户"),
        },
        use_container_width=True,
        key="account_editor",
        hide_index=True,
    )

    # 使用更健壮的方式来检测和处理变更
    if not accounts_df.equals(edited_df):
        changes_found = False
        for i in edited_df.index:
            orig_row = accounts_df.loc[i]
            new_row = edited_df.loc[i]
            if not orig_row.equals(new_row):
                changes_found = True
                account_id = new_row['ID']
                updates = {
                    'name': new_row['账户名称'],
                    'account_type': new_row['类型'],
                    'status': new_row['状态'],
                    'aweme_id': new_row['抖音号UID'],
                    'is_test_account': new_row['测试账户']
                }
                update_single_account(account_id, updates)
        
        if changes_found:
            st.success("账户信息已更新！")
            time.sleep(1)
            st.cache_data.clear() # 清理缓存以确保下次能获取最新数据
            st.rerun()

def render_launch_center_page():
    st.header("🚀 投放中心 (旧版)")
    st.markdown("这里是执行所有与广告投放、计划创建和优化相关操作的核心区域。")

    # 显示当前选中的账户信息
    account_info = get_current_account_info()
    if account_info:
        col1, col2, col3 = st.columns([2, 2, 1])
        with col1:
            star = "⭐ " if account_info['is_favorite'] else ""
            st.info(f"📋 当前账户: {star}{account_info['name']}")
        with col2:
            st.info(f"🆔 千川ID: {account_info['account_id']}")
        with col3:
            aweme_status = "📱 已授权" if account_info['aweme_id'] else "❌ 未授权"
            st.info(f"抖音号: {aweme_status}")

    with st.expander("手动批量投放", expanded=True):
        st.info("选择一个包含视频素材的本地文件夹，并将其批量投放到当前选中的广告账户。")

        # 检查是否选择了账户
        current_account = require_account_selection("请先在左侧栏选择一个广告账户进行投放操作")

        with st.form("manual_launch_form"):
            path = st.text_input("素材文件夹路径", placeholder="例如: D:/videos/new_batch")
            
            strategy_choices = ['custom_deal', 'managed_deal', 'managed_roi', 'new_customer_deal', 'new_customer_roi', 'smart_mix']
            strategy = st.selectbox("选择投放策略", options=strategy_choices)
            
            col1, col2, col3 = st.columns(3)
            with col1:
                budget = st.number_input("计划日预算 (可选)", value=None, placeholder="留空则随机")
            with col2:
                cpa = st.number_input("CPA出价 (可选)", value=None, placeholder="留空则随机")
            with col3:
                roi = st.number_input("ROI目标 (可选)", value=None, placeholder="留空则随机")

            # 添加高级选项
            st.subheader("🔧 高级选项")
            col1, col2 = st.columns(2)
            with col1:
                use_similarity_grouping = st.checkbox("启用相似度智能分组", value=False,
                                                    help="优先将相似的视频分到同一组，提高投放效果")
            with col2:
                plan_creation_interval = st.number_input("计划创建间隔 (秒)", value=25, min_value=5, max_value=300,
                                                       help="每个计划创建之间的等待时间")

            submitted = st.form_submit_button("开始投放")
            if submitted:
                if not path:
                    st.error("素材文件夹路径不能为空！")
                else:
                    advertiser_id = current_account.account_id_qc
                    with st.spinner(f"正在向账户 {current_account.name} ({advertiser_id}) 投放素材..."):
                        run_with_log_capture(
                            manual_launch_tool.run_manual_launch,
                            path=path, advertiser_id=int(advertiser_id), strategy=strategy,
                            budget=budget, cpa=cpa, roi=roi,
                            use_similarity_grouping=use_similarity_grouping,
                            plan_creation_interval=plan_creation_interval
                        )
    
    with st.expander("🔄 批量账户内计划复制"):
        st.info("从多个源账户查找符合特定条件的现有计划，并在各自账户内进行复制，大幅提升批量操作效率。")
        st.warning("⚠️ 根据千川平台规则限制，计划复制只能在同一个广告账户内进行")

        # 创建选项卡
        tab1, tab2 = st.tabs(["📋 单账户复制", "🚀 批量账户复制"])

        with tab1:
            st.markdown("### 单账户复制")
            st.info("在当前选中账户内查找计划并在该账户内复制")
            render_single_account_replication()

        with tab2:
            st.markdown("### 批量账户复制")
            st.info("从多个源账户批量查找计划，每个账户的计划在该账户内复制")
            render_batch_cross_account_replication()

    # 优质素材收割与再投放功能
    with st.expander("优质素材收割与再投放"):
        render_harvest_and_relaunch()

def render_single_account_replication():
    """渲染单账户复制功能"""

    # 初始化session state（添加防护机制）
    def init_single_state():
        """初始化单账户复制状态变量"""
        defaults = {
            'single_replication_step': 1,
            'single_found_campaigns': [],
            'single_selected_campaigns': [],
            'single_task_running': False,
            'single_task_paused': False,
            'single_progress': {'current': 0, 'total': 0, 'logs': []}
        }

        for key, default_value in defaults.items():
            if key not in st.session_state:
                st.session_state[key] = default_value

    init_single_state()

    # 状态一致性检查
    if st.session_state.single_replication_step < 1 or st.session_state.single_replication_step > 3:
        st.session_state.single_replication_step = 1

    # 步骤指示器
    step_cols = st.columns(3)
    with step_cols[0]:
        if st.session_state.single_replication_step >= 1:
            st.success("✅ 1. 设置筛选条件")
        else:
            st.info("1. 设置筛选条件")
    with step_cols[1]:
        if st.session_state.single_replication_step >= 2:
            st.success("✅ 2. 选择计划")
        else:
            st.info("2. 选择计划")
    with step_cols[2]:
        if st.session_state.single_replication_step >= 3:
            st.success("✅ 3. 执行复制")
        else:
            st.info("3. 执行复制")

    st.divider()

    # 第一步：设置筛选条件
    if st.session_state.single_replication_step == 1:
        st.subheader("🔍 第一步：设置筛选条件")

        # 检查是否选择了账户
        current_account = require_account_selection("请先在左侧栏选择一个广告账户作为源账户")

        with st.form("single_search_campaigns_form"):
            # 显示源广告账户
            st.subheader("源广告账户")
            st.info(f"📤 源账户: {current_account.name} ({current_account.account_id_qc})")
            st.markdown("*使用当前选中的账户作为源账户，如需更改请在左侧栏重新选择*")

            st.subheader("源计划筛选条件 (至少提供一项)")

            # 计划状态下拉选择
            status_options = {
                "": "不筛选",
                "DELIVERY_OK": "投放中",
                "DISABLE": "已暂停",
                "LIVE_ROOM_OFF": "关联直播间未开播",
                "CAMPAIGN_DISABLE": "已被广告组暂停",
                "BUDGET_EXCEED": "预算超限",
                "AUDIT_REJECT": "审核拒绝",
                "NO_SCHEDULE": "不在投放时段"
            }

            selected_status_display = st.selectbox(
                "按计划状态筛选 (可选)",
                options=list(status_options.values()),
                help="选择要筛选的计划状态"
            )

            # 获取实际的状态值
            source_status = None
            for status_code, status_name in status_options.items():
                if status_name == selected_status_display and status_code:
                    source_status = status_code
                    break

            # 抖音号筛选
            source_aweme_id = st.text_input(
                "按抖音号UID筛选 (可选)",
                placeholder="输入抖音号UID",
                help="输入具体的抖音号UID进行筛选"
            )

            # 时间筛选
            col1, col2 = st.columns(2)
            with col1:
                start_date = st.date_input(
                    "计划创建开始日期 (可选)",
                    value=None,
                    help="筛选在此日期之后创建的计划"
                )
            with col2:
                end_date = st.date_input(
                    "计划创建结束日期 (可选)",
                    value=None,
                    help="筛选在此日期之前创建的计划"
                )

            search_submitted = st.form_submit_button("🔍 查找符合条件的计划")

            if search_submitted:
                if not any([source_status, source_aweme_id, start_date, end_date]):
                    st.error("必须提供至少一个源计划筛选条件！")
                else:
                    # 获取当前选中的账户ID
                    selected_account_id = current_account.account_id_qc

                    with st.spinner(f"正在从账户 {current_account.name} 查找符合条件的计划..."):
                        try:
                            # 转换日期格式
                            start_date_str = start_date.strftime('%Y-%m-%d') if start_date else None
                            end_date_str = end_date.strftime('%Y-%m-%d') if end_date else None

                            # 调用查找函数，传入账户ID
                            campaigns = replicate_plan_tool.find_campaigns_by_criteria(
                                source_account_id=selected_account_id,
                                source_status=source_status,
                                source_aweme_id=source_aweme_id if source_aweme_id else None,
                                start_date=start_date_str,
                                end_date=end_date_str
                            )

                            if campaigns:
                                st.session_state.single_found_campaigns = campaigns
                                st.session_state.single_selected_source_account = f"⭐ {current_account.name} ({current_account.account_id_qc})" if getattr(current_account, 'is_favorite', False) else f"{current_account.name} ({current_account.account_id_qc})"
                                st.session_state.single_replication_step = 2
                                st.success(f"✅ 找到 {len(campaigns)} 个符合条件的计划")
                                st.rerun()
                            else:
                                st.warning("没有找到符合条件的计划")

                        except Exception as e:
                            st.error(f"查找计划失败: {str(e)}")

    # 第二步：选择计划
    elif st.session_state.single_replication_step == 2:
        st.subheader("📋 第二步：选择要复制的计划")

        if st.session_state.single_found_campaigns:
            st.info(f"找到 {len(st.session_state.single_found_campaigns)} 个符合条件的计划，请选择要复制的计划：")

            # 创建DataFrame显示计划列表
            campaigns_df = pd.DataFrame(st.session_state.single_found_campaigns)

            # 添加选择列
            campaigns_df.insert(0, '选择', True)  # 默认全选

            # 显示可编辑的数据表
            edited_df = st.data_editor(
                campaigns_df,
                column_config={
                    "选择": st.column_config.CheckboxColumn(
                        "选择",
                        help="选择要复制的计划",
                        default=True,
                    ),
                    "campaign_id": "计划ID",
                    "name": "计划名称",
                    "status_display": "状态",
                    "account_name": "账户名称",
                    "principal_name": "主体名称",
                    "aweme_id": "抖音号",
                    "created_at": "创建时间"
                },
                disabled=["campaign_id", "name", "status_display", "account_name", "principal_name", "aweme_id", "created_at"],
                hide_index=True,
                use_container_width=True,
                column_order=["选择", "name", "status_display", "account_name", "principal_name", "aweme_id", "created_at"]
            )

            # 获取选中的计划
            selected_campaigns = edited_df[edited_df['选择'] == True].to_dict('records')

            col1, col2, col3 = st.columns([1, 1, 1])

            with col1:
                if st.button("⬅️ 返回上一步", key="single_step2_back"):
                    st.session_state.single_replication_step = 1
                    st.rerun()

            with col2:
                st.info(f"已选择 {len(selected_campaigns)} 个计划")

            with col3:
                if st.button("下一步 ➡️", disabled=len(selected_campaigns) == 0, key="single_step2_next"):
                    st.session_state.single_selected_campaigns = selected_campaigns
                    st.session_state.single_replication_step = 3
                    st.rerun()
        else:
            st.error("没有找到计划数据")
            if st.button("⬅️ 返回上一步", key="single_step2_back_error"):
                st.session_state.single_replication_step = 1
                st.rerun()

    # 第三步：执行复制
    elif st.session_state.single_replication_step == 3:
        st.subheader("🚀 第三步：执行复制")

        st.info(f"准备复制 {len(st.session_state.single_selected_campaigns)} 个计划")

        # 显示选中的计划
        st.subheader("查看选中的计划")
        for campaign in st.session_state.single_selected_campaigns:
            st.write(f"• {campaign['name']} (ID: {campaign['campaign_id']})")

        # 任务控制状态已在函数开始时初始化

        with st.form("single_execute_replication_form"):
            st.subheader("复制设置")

            # 显示目标账户信息（与源账户相同）
            current_account = require_account_selection("请先在左侧栏选择一个广告账户")
            st.info(f"🎯 目标账户: {current_account.name} ({current_account.account_id_qc})")
            st.markdown("*根据千川平台规则，计划复制只能在同一广告账户内进行*")

            st.subheader("🧹 智能名称清理设置")

            # 名称清理选项
            col1, col2 = st.columns(2)
            with col1:
                clean_prefixes = st.checkbox(
                    "清理历史前缀",
                    value=True,
                    help="自动识别并移除计划名称中的历史前缀（如'测试_'、'复制_'等）"
                )
            with col2:
                preserve_standard_format = st.checkbox(
                    "保留标准格式",
                    value=True,
                    help="保留标准的日期/策略/创作者/日期/时间格式"
                )

            # 新计划名称设置
            new_plan_prefix = st.text_input(
                "新计划名称前缀 (可选)",
                placeholder="例如: 测试_",
                help="为所有复制的计划添加统一前缀"
            )

            # 任务控制按钮
            col1, col2, col3 = st.columns([1, 1, 1])

            with col1:
                preview_names = st.form_submit_button("🔍 预览名称清理", type="secondary")

            with col2:
                execute_submitted = st.form_submit_button("🚀 开始执行复制", type="primary")

            with col3:
                back_submitted = st.form_submit_button("⬅️ 返回上一步", type="secondary")

            # 处理按钮点击
            if back_submitted:
                st.session_state.single_replication_step = 2
                st.success("⬅️ 正在返回上一步...")
                st.rerun()

            # 处理预览请求
            if preview_names:
                with st.spinner("正在预览名称清理结果..."):
                    try:
                        preview_result = replicate_plan_tool.preview_name_cleaning(
                            selected_campaigns=st.session_state.single_selected_campaigns,
                            new_plan_prefix=new_plan_prefix if new_plan_prefix else "",
                            clean_prefixes=clean_prefixes,
                            preserve_standard_format=preserve_standard_format
                        )

                        if preview_result['success']:
                            st.subheader("📋 名称清理预览结果")
                            preview_data = preview_result['preview']
                            stats = preview_data['statistics']

                            # 显示统计信息
                            col1, col2, col3, col4 = st.columns(4)
                            with col1:
                                st.metric("总计划数", stats['total_count'])
                            with col2:
                                st.metric("检测到前缀", stats['prefix_detected_count'])
                            with col3:
                                st.metric("标准格式", stats['standard_format_count'])
                            with col4:
                                st.metric("重名冲突", stats['conflict_count'])

                        else:
                            st.error(f"预览失败: {preview_result['message']}")

                    except Exception as e:
                        st.error(f"预览失败: {str(e)}")

            # 处理执行请求
            if execute_submitted:
                # 启动单账户复制任务
                st.session_state.single_task_running = True
                st.session_state.single_task_paused = False

                # 计算总任务数（在同一账户内复制）
                total_tasks = len(st.session_state.single_selected_campaigns)
                st.session_state.single_progress = {
                    'current': 0,
                    'total': total_tasks,
                    'logs': [],
                    'success_count': 0,
                    'failed_count': 0
                }

                st.rerun()

        # 任务执行状态显示
        if st.session_state.single_task_running:
            st.subheader("🎯 单账户复制任务执行中")

            # 任务控制按钮
            col1, col2, col3 = st.columns(3)

            with col1:
                if st.session_state.single_task_paused:
                    if st.button("▶️ 继续任务", use_container_width=True, key="single_resume"):
                        st.session_state.single_task_paused = False
                        st.rerun()
                else:
                    if st.button("⏸️ 暂停任务", use_container_width=True, key="single_pause"):
                        st.session_state.single_task_paused = True
                        st.rerun()

            with col2:
                if st.button("🛑 结束任务", use_container_width=True, key="single_stop"):
                    st.session_state.single_task_running = False
                    st.session_state.single_task_paused = False
                    st.warning("⚠️ 任务已被用户手动结束")
                    st.rerun()

            with col3:
                task_status = "⏸️ 已暂停" if st.session_state.single_task_paused else "🔄 运行中"
                st.info(f"任务状态: {task_status}")

            # 执行单账户复制逻辑（如果任务运行中且未暂停）
            if not st.session_state.single_task_paused:
                execute_single_replication_task(
                    new_plan_prefix,
                    clean_prefixes,
                    preserve_standard_format
                )

        # 导航按钮（仅在任务未运行时显示）
        if not st.session_state.single_task_running:
            col1, col2 = st.columns([1, 1])
            with col1:
                if st.button("⬅️ 返回上一步", key="single_back_nav"):
                    st.session_state.single_replication_step = 2
                    st.rerun()

            with col2:
                if st.button("🔄 重新开始", key="single_restart_nav"):
                    st.session_state.single_replication_step = 1
                    st.session_state.single_found_campaigns = []
                    st.session_state.single_selected_campaigns = []
                    st.rerun()

def execute_single_replication_task(new_plan_prefix, clean_prefixes, preserve_standard_format):
    """执行单账户复制任务"""
    try:
        # 检查任务是否应该继续
        if not st.session_state.single_task_running or st.session_state.single_task_paused:
            return

        progress = st.session_state.single_progress

        # 如果任务刚开始，添加开始日志
        if progress['current'] == 0:
            progress['logs'].append({
                'type': 'info',
                'message': f"开始单账户内复制任务：{len(st.session_state.single_selected_campaigns)} 个计划"
            })

        # 获取当前账户作为目标账户（同一账户内复制）
        current_account = require_account_selection("请先在左侧栏选择一个广告账户")
        target_account_ids = [current_account.account_id_qc]

        # 执行复制
        with st.spinner("正在执行单账户内复制..."):
            try:
                result = replicate_plan_tool.run_replication(
                    target_account_ids=target_account_ids,
                    new_plan_prefix=new_plan_prefix if new_plan_prefix else None,
                    selected_campaigns=st.session_state.single_selected_campaigns,
                    clean_prefixes=clean_prefixes,
                    preserve_standard_format=preserve_standard_format
                )

                if result['success']:
                    # 任务成功完成
                    st.session_state.single_task_running = False
                    progress['current'] = progress['total']
                    progress['logs'].append({
                        'type': 'success',
                        'message': f"✅ 单账户复制任务完成: {result['message']}"
                    })

                    st.success(f"🎉 单账户复制任务完成！{result['message']}")

                    # 显示完成后的操作选项
                    col1, col2 = st.columns(2)
                    with col1:
                        if st.button("🔄 开始新的复制", key="single_new_task"):
                            # 重置状态
                            st.session_state.single_replication_step = 1
                            st.session_state.single_found_campaigns = []
                            st.session_state.single_selected_campaigns = []
                            st.session_state.single_task_running = False
                            st.session_state.single_task_paused = False
                            st.session_state.single_progress = {'current': 0, 'total': 0, 'logs': []}
                            st.rerun()

                    with col2:
                        if st.button("📊 查看详细结果", key="single_view_results"):
                            st.info("详细结果已记录在执行日志中")
                else:
                    # 任务失败
                    st.session_state.single_task_running = False
                    progress['logs'].append({
                        'type': 'error',
                        'message': f"❌ 单账户复制任务失败: {result['message']}"
                    })
                    st.error(f"❌ 单账户复制失败: {result['message']}")

            except Exception as e:
                # 执行异常
                st.session_state.single_task_running = False
                progress['logs'].append({
                    'type': 'error',
                    'message': f"❌ 执行异常: {str(e)}"
                })
                st.error(f"执行单账户复制失败: {str(e)}")

    except Exception as e:
        st.error(f"单账户复制任务执行器异常: {str(e)}")

def render_batch_cross_account_replication():
    """渲染批量跨账户复制功能"""
    # 初始化session state（添加防护机制）
    def init_batch_state():
        """安全的批量复制状态初始化"""
        defaults = {
            'batch_replication_step': 1,
            'batch_found_campaigns': [],
            'batch_selected_campaigns': [],
            'batch_source_accounts': [],
            'batch_task_running': False,
            'batch_task_paused': False,
            'batch_progress': {'current': 0, 'total': 0, 'logs': [], 'success_count': 0, 'failed_count': 0},
            'batch_filter_criteria': {},
            'batch_last_update_time': 0,
            'batch_error_state': False,
            'batch_error_message': "",
            'batch_last_execution_time': 0
        }

        # 安全初始化，避免覆盖现有有效状态
        for key, default_value in defaults.items():
            if key not in st.session_state:
                st.session_state[key] = default_value
            elif key == 'batch_progress' and not isinstance(st.session_state[key], dict):
                # 修复损坏的进度状态
                st.session_state[key] = default_value

        # 状态一致性检查
        if st.session_state.batch_replication_step < 1 or st.session_state.batch_replication_step > 4:
            st.session_state.batch_replication_step = 1
            st.session_state.batch_error_state = True
            st.session_state.batch_error_message = "检测到状态异常，已重置到第一步"

    def reset_batch_state():
        """重置批量复制状态"""
        keys_to_reset = [
            'batch_replication_step', 'batch_found_campaigns', 'batch_selected_campaigns',
            'batch_source_accounts', 'batch_task_running', 'batch_task_paused',
            'batch_progress', 'batch_filter_criteria', 'batch_error_state', 'batch_error_message'
        ]

        for key in keys_to_reset:
            if key in st.session_state:
                del st.session_state[key]

        # 重新初始化
        init_batch_state()

    init_batch_state()

    # 显示错误状态（如果有）
    if st.session_state.batch_error_state:
        st.error(f"⚠️ {st.session_state.batch_error_message}")
        if st.button("🔄 重置状态"):
            reset_batch_state()
            st.rerun()

    # 步骤指示器
    step_cols = st.columns(4)
    with step_cols[0]:
        if st.session_state.batch_replication_step >= 1:
            st.success("✅ 1. 选择源账户")
        else:
            st.info("1. 选择源账户")
    with step_cols[1]:
        if st.session_state.batch_replication_step >= 2:
            st.success("✅ 2. 设置筛选条件")
        else:
            st.info("2. 设置筛选条件")
    with step_cols[2]:
        if st.session_state.batch_replication_step >= 3:
            st.success("✅ 3. 选择计划")
        else:
            st.info("3. 选择计划")
    with step_cols[3]:
        if st.session_state.batch_replication_step >= 4:
            st.success("✅ 4. 执行复制")
        else:
            st.info("4. 执行复制")

    st.divider()

    # 第一步：选择多个源账户
    if st.session_state.batch_replication_step == 1:
        st.subheader("🏢 第一步：选择源广告账户")
        st.info("选择要从中复制计划的源广告账户，支持选择多个账户进行批量操作")

        # 使用session_state缓存选择结果，避免表单冲突
        if 'batch_temp_source_accounts' not in st.session_state:
            st.session_state.batch_temp_source_accounts = []

        with st.form("batch_select_source_accounts_form"):
            try:
                # 多账户选择器
                selected_source_accounts = create_multi_account_selector(
                    key="batch_source_accounts_temp",
                    label="选择源广告账户（支持多选）",
                    help_text="⭐ 标记表示收藏账户，选择要复制计划的源账户",
                    show_filter=True,
                    show_stats=True,
                    in_form=True
                )

                # 缓存选择结果
                if selected_source_accounts:
                    st.session_state.batch_temp_source_accounts = selected_source_accounts

                # 显示当前选择
                if st.session_state.batch_temp_source_accounts:
                    st.subheader("📋 已选择的源账户")
                    for i, account in enumerate(st.session_state.batch_temp_source_accounts, 1):
                        is_favorite = getattr(account, 'is_favorite', False)
                        star = "⭐ " if is_favorite else ""
                        st.write(f"{i}. {star}{account.name} ({account.account_id_qc})")

                next_step_submitted = st.form_submit_button("下一步：设置筛选条件 ➡️")

                if next_step_submitted:
                    if not st.session_state.batch_temp_source_accounts:
                        st.error("必须选择至少一个源广告账户！")
                    else:
                        # 确认选择
                        st.session_state.batch_source_accounts = st.session_state.batch_temp_source_accounts
                        st.session_state.batch_replication_step = 2
                        st.session_state.batch_last_update_time = time.time()
                        st.success(f"✅ 正在跳转到第2步...")
                        time.sleep(0.1)  # 短暂延迟避免冲突
                        st.rerun()

            except Exception as e:
                st.error(f"源账户选择失败: {str(e)}")
                st.session_state.batch_error_state = True
                st.session_state.batch_error_message = str(e)

    # 第二步：设置统一筛选条件
    elif st.session_state.batch_replication_step == 2:
        st.subheader("🔍 第二步：设置统一筛选条件")
        st.info("为所有源账户设置相同的计划筛选条件，系统将在每个源账户中查找符合条件的计划")

        # 显示已选择的源账户
        st.subheader("📤 已选择的源账户")
        source_accounts_info = []
        for account in st.session_state.batch_source_accounts:
            is_favorite = getattr(account, 'is_favorite', False)
            star = "⭐ " if is_favorite else ""
            source_accounts_info.append(f"{star}{account.name} ({account.account_id_qc})")
        st.info(" | ".join(source_accounts_info))

        with st.form("batch_filter_criteria_form"):
            st.subheader("统一筛选条件 (至少提供一项)")

            # 计划状态下拉选择
            status_options = {
                "": "不筛选",
                "DELIVERY_OK": "投放中",
                "DISABLE": "已暂停",
                "LIVE_ROOM_OFF": "关联直播间未开播",
                "CAMPAIGN_DISABLE": "已被广告组暂停",
                "BUDGET_EXCEED": "预算超限",
                "AUDIT_REJECT": "审核拒绝",
                "NO_SCHEDULE": "不在投放时段"
            }

            selected_status_display = st.selectbox(
                "按计划状态筛选 (可选)",
                options=list(status_options.values()),
                help="选择要筛选的计划状态，将应用到所有源账户"
            )

            # 获取实际的状态值
            source_status = None
            for status_code, status_name in status_options.items():
                if status_name == selected_status_display and status_code:
                    source_status = status_code
                    break

            # 抖音号筛选
            source_aweme_id = st.text_input(
                "按抖音号UID筛选 (可选)",
                placeholder="输入抖音号UID",
                help="输入具体的抖音号UID进行筛选，将应用到所有源账户"
            )

            # 时间筛选
            col1, col2 = st.columns(2)
            with col1:
                start_date = st.date_input(
                    "计划创建开始日期 (可选)",
                    value=None,
                    help="筛选在此日期之后创建的计划"
                )
            with col2:
                end_date = st.date_input(
                    "计划创建结束日期 (可选)",
                    value=None,
                    help="筛选在此日期之前创建的计划"
                )

            col1, col2 = st.columns([1, 1])
            with col1:
                back_submitted = st.form_submit_button("⬅️ 返回上一步")
            with col2:
                search_submitted = st.form_submit_button("🔍 查找符合条件的计划")

            if back_submitted:
                try:
                    st.session_state.batch_replication_step = 1
                    st.session_state.batch_last_update_time = time.time()
                    st.success("⬅️ 正在返回上一步...")
                    time.sleep(0.1)  # 短暂延迟避免冲突
                    st.rerun()
                except Exception as e:
                    st.error(f"返回上一步失败: {str(e)}")

            if search_submitted:
                try:
                    if not any([source_status, source_aweme_id, start_date, end_date]):
                        st.error("必须提供至少一个筛选条件！")
                    else:
                        # 保存筛选条件
                        st.session_state.batch_filter_criteria = {
                            'source_status': source_status,
                            'source_aweme_id': source_aweme_id if source_aweme_id else None,
                            'start_date': start_date.strftime('%Y-%m-%d') if start_date else None,
                            'end_date': end_date.strftime('%Y-%m-%d') if end_date else None
                        }

                        # 从所有源账户查找计划
                        all_campaigns = []
                        with st.spinner("正在从所有源账户查找符合条件的计划..."):
                            try:
                                for account in st.session_state.batch_source_accounts:
                                    campaigns = replicate_plan_tool.find_campaigns_by_criteria(
                                        source_account_id=account.account_id_qc,
                                        source_status=source_status,
                                        source_aweme_id=source_aweme_id if source_aweme_id else None,
                                        start_date=st.session_state.batch_filter_criteria['start_date'],
                                        end_date=st.session_state.batch_filter_criteria['end_date']
                                    )
                                    if campaigns:
                                        # 为每个计划添加源账户信息
                                        for campaign in campaigns:
                                            campaign['source_account_name'] = account.name
                                            campaign['source_account_id'] = account.account_id_qc
                                        all_campaigns.extend(campaigns)

                                if all_campaigns:
                                    st.session_state.batch_found_campaigns = all_campaigns
                                    st.session_state.batch_replication_step = 3
                                    st.session_state.batch_last_update_time = time.time()
                                    st.success(f"✅ 从 {len(st.session_state.batch_source_accounts)} 个源账户找到 {len(all_campaigns)} 个符合条件的计划")
                                    st.info("🔄 正在跳转到下一步...")
                                    time.sleep(0.1)  # 短暂延迟避免冲突
                                    st.rerun()
                                else:
                                    st.warning("在所有源账户中都没有找到符合条件的计划")

                            except Exception as e:
                                st.error(f"查找计划失败: {str(e)}")
                                st.session_state.batch_error_state = True
                                st.session_state.batch_error_message = f"查找计划失败: {str(e)}"

                except Exception as e:
                    st.error(f"筛选条件处理失败: {str(e)}")
                    st.session_state.batch_error_state = True
                    st.session_state.batch_error_message = str(e)

    # 第三步：选择计划
    elif st.session_state.batch_replication_step == 3:
        st.subheader("📋 第三步：选择要复制的计划")

        if not st.session_state.batch_found_campaigns:
            st.error("没有找到计划数据")
            if st.button("⬅️ 返回上一步"):
                st.session_state.batch_replication_step = 2
                st.session_state.batch_last_update_time = time.time()
                time.sleep(0.1)
                st.rerun()
            return

        st.info(f"从 {len(st.session_state.batch_source_accounts)} 个源账户找到 {len(st.session_state.batch_found_campaigns)} 个符合条件的计划")

        # 使用简化的选择机制，避免data_editor的复杂性
        if 'batch_campaign_selections' not in st.session_state:
            # 初始化选择状态（默认全选）
            st.session_state.batch_campaign_selections = {
                str(i): True for i in range(len(st.session_state.batch_found_campaigns))
            }

        # 批量操作按钮
        col1, col2, col3 = st.columns(3)
        with col1:
            if st.button("✅ 全选"):
                st.session_state.batch_campaign_selections = {
                    str(i): True for i in range(len(st.session_state.batch_found_campaigns))
                }
                st.rerun()
        with col2:
            if st.button("❌ 全不选"):
                st.session_state.batch_campaign_selections = {
                    str(i): False for i in range(len(st.session_state.batch_found_campaigns))
                }
                st.rerun()
        with col3:
            if st.button("🔄 反选"):
                st.session_state.batch_campaign_selections = {
                    k: not v for k, v in st.session_state.batch_campaign_selections.items()
                }
                st.rerun()

        # 分页显示计划列表
        campaigns_per_page = 20
        total_campaigns = len(st.session_state.batch_found_campaigns)
        total_pages = (total_campaigns + campaigns_per_page - 1) // campaigns_per_page

        if 'batch_current_page' not in st.session_state:
            st.session_state.batch_current_page = 0

        # 分页控制
        if total_pages > 1:
            col1, col2, col3 = st.columns([1, 2, 1])
            with col1:
                if st.button("⬅️ 上一页", disabled=st.session_state.batch_current_page == 0):
                    st.session_state.batch_current_page -= 1
                    st.rerun()
            with col2:
                st.write(f"第 {st.session_state.batch_current_page + 1} 页，共 {total_pages} 页")
            with col3:
                if st.button("下一页 ➡️", disabled=st.session_state.batch_current_page >= total_pages - 1):
                    st.session_state.batch_current_page += 1
                    st.rerun()

        # 显示当前页的计划
        start_idx = st.session_state.batch_current_page * campaigns_per_page
        end_idx = min(start_idx + campaigns_per_page, total_campaigns)
        current_page_campaigns = st.session_state.batch_found_campaigns[start_idx:end_idx]

        # 使用checkbox显示计划
        for i, campaign in enumerate(current_page_campaigns):
            actual_idx = start_idx + i
            key = str(actual_idx)

            col1, col2 = st.columns([1, 10])
            with col1:
                selected = st.checkbox(
                    "",
                    value=st.session_state.batch_campaign_selections.get(key, True),
                    key=f"campaign_select_{actual_idx}"
                )
                st.session_state.batch_campaign_selections[key] = selected

            with col2:
                st.write(f"**{campaign['name']}**")
                st.write(f"状态: {campaign.get('status_display', 'N/A')} | 源账户: {campaign.get('source_account_name', 'N/A')}")

        # 统计选中的计划
        selected_count = sum(1 for v in st.session_state.batch_campaign_selections.values() if v)

        # 按源账户分组显示统计
        if selected_count > 0:
            st.subheader("📊 选择统计")
            account_stats = {}
            for i, campaign in enumerate(st.session_state.batch_found_campaigns):
                if st.session_state.batch_campaign_selections.get(str(i), False):
                    account_name = campaign['source_account_name']
                    if account_name not in account_stats:
                        account_stats[account_name] = 0
                    account_stats[account_name] += 1

            cols = st.columns(min(len(account_stats), 4))
            for i, (account_name, count) in enumerate(account_stats.items()):
                with cols[i % 4]:
                    st.metric(account_name, f"{count} 个计划")

        # 导航按钮
        col1, col2, col3 = st.columns([1, 1, 1])
        with col1:
            if st.button("⬅️ 返回上一步"):
                st.session_state.batch_replication_step = 2
                st.session_state.batch_last_update_time = time.time()
                time.sleep(0.1)
                st.rerun()
        with col2:
            st.info(f"已选择 {selected_count} 个计划")
        with col3:
            if st.button("下一步 ➡️", disabled=selected_count == 0):
                # 保存选中的计划
                selected_campaigns = []
                for i, campaign in enumerate(st.session_state.batch_found_campaigns):
                    if st.session_state.batch_campaign_selections.get(str(i), False):
                        selected_campaigns.append(campaign)

                st.session_state.batch_selected_campaigns = selected_campaigns
                st.session_state.batch_replication_step = 4
                st.session_state.batch_last_update_time = time.time()
                time.sleep(0.1)
                st.rerun()

    # 第四步：执行复制
    elif st.session_state.batch_replication_step == 4:
        st.subheader("🚀 第四步：执行批量账户内复制")

        st.info(f"准备将 {len(st.session_state.batch_selected_campaigns)} 个计划在各自账户内进行复制")

        # 显示选中的计划统计
        st.subheader("📊 复制计划统计")
        account_stats = {}
        for campaign in st.session_state.batch_selected_campaigns:
            account_name = campaign['source_account_name']
            if account_name not in account_stats:
                account_stats[account_name] = []
            account_stats[account_name].append(campaign['name'])

        # 使用折叠式显示替代嵌套expander
        for account_name, campaigns in account_stats.items():
            st.markdown(f"**📁 {account_name} ({len(campaigns)} 个计划)**")

            # 创建两列显示计划名称
            if campaigns:
                # 将计划分成两列显示
                mid_point = len(campaigns) // 2
                col1, col2 = st.columns(2)

                with col1:
                    for campaign_name in campaigns[:mid_point]:
                        st.write(f"• {campaign_name}")

                with col2:
                    for campaign_name in campaigns[mid_point:]:
                        st.write(f"• {campaign_name}")

            st.markdown("---")  # 分隔线

        with st.form("batch_execute_replication_form"):
            st.subheader("复制设置")

            # 显示复制规则说明
            st.info("🎯 复制规则：根据千川平台限制，每个源账户的计划将在该账户内部进行复制")

            # 显示源账户统计
            st.subheader("📊 源账户统计")
            for account in st.session_state.batch_source_accounts:
                is_favorite = getattr(account, 'is_favorite', False)
                star = "⭐ " if is_favorite else ""
                st.write(f"• {star}{account.name} ({account.account_id_qc}) - 将在该账户内复制计划")

            st.subheader("🧹 智能名称清理设置")

            # 名称清理选项
            col1, col2 = st.columns(2)
            with col1:
                clean_prefixes = st.checkbox(
                    "清理历史前缀",
                    value=True,
                    help="自动识别并移除计划名称中的历史前缀（如'测试_'、'复制_'等）"
                )
            with col2:
                preserve_standard_format = st.checkbox(
                    "保留标准格式",
                    value=True,
                    help="保留标准的日期/策略/创作者/日期/时间格式"
                )

            # 新计划名称设置
            new_plan_prefix = st.text_input(
                "新计划名称前缀 (可选)",
                placeholder="例如: 批量复制_",
                help="为所有复制的计划添加统一前缀"
            )

            st.subheader("📱 抖音号设置")

            # 抖音号设置选项
            aweme_setting_option = st.radio(
                "抖音号处理方式",
                options=["使用数据库中的最新抖音号", "保持原计划的抖音号", "指定统一的抖音号"],
                index=0,
                help="选择复制计划时如何处理抖音号"
            )

            target_aweme_id = None
            if aweme_setting_option == "指定统一的抖音号":
                target_aweme_id = st.text_input(
                    "目标抖音号",
                    placeholder="输入抖音号，例如: 7410578812859860027",
                    help="为所有复制的计划指定统一的抖音号"
                )

                if target_aweme_id and not target_aweme_id.isdigit():
                    st.error("⚠️ 抖音号必须是纯数字格式")
                    target_aweme_id = None

            # 显示抖音号处理说明
            if aweme_setting_option == "使用数据库中的最新抖音号":
                st.info("💡 将使用数据库中每个账户的最新抖音号，如果没有则保持原计划的抖音号")
            elif aweme_setting_option == "保持原计划的抖音号":
                st.info("💡 复制的计划将保持与原计划相同的抖音号")
            elif aweme_setting_option == "指定统一的抖音号" and target_aweme_id:
                st.success(f"✅ 所有复制的计划将使用抖音号: {target_aweme_id}")

            # 任务控制按钮
            col1, col2, col3, col4 = st.columns([1, 1, 1, 1])

            with col1:
                back_submitted = st.form_submit_button("⬅️ 返回上一步", type="secondary")

            with col2:
                preview_names = st.form_submit_button("🔍 预览名称清理", type="secondary")

            with col3:
                execute_submitted = st.form_submit_button("🚀 开始批量复制", type="primary")

            with col4:
                reset_submitted = st.form_submit_button("🔄 重新开始", type="secondary")

            # 处理按钮点击
            if back_submitted:
                st.session_state.batch_replication_step = 3
                st.success("⬅️ 正在返回上一步...")
                st.rerun()

            if reset_submitted:
                # 重置所有状态
                st.session_state.batch_replication_step = 1
                st.session_state.batch_found_campaigns = []
                st.session_state.batch_selected_campaigns = []
                st.session_state.batch_source_accounts = []
                st.session_state.batch_task_running = False
                st.session_state.batch_task_paused = False
                st.session_state.batch_progress = {'current': 0, 'total': 0, 'logs': []}
                st.rerun()

            # 处理预览请求
            if preview_names:
                with st.spinner("正在预览名称清理结果..."):
                    try:
                        preview_result = replicate_plan_tool.preview_name_cleaning(
                            selected_campaigns=st.session_state.batch_selected_campaigns,
                            new_plan_prefix=new_plan_prefix if new_plan_prefix else "",
                            clean_prefixes=clean_prefixes,
                            preserve_standard_format=preserve_standard_format
                        )

                        if preview_result['success']:
                            st.subheader("📋 名称清理预览结果")
                            preview_data = preview_result['preview']
                            stats = preview_data['statistics']

                            # 显示统计信息
                            col1, col2, col3, col4 = st.columns(4)
                            with col1:
                                st.metric("总计划数", stats['total_count'])
                            with col2:
                                st.metric("检测到前缀", stats['prefix_detected_count'])
                            with col3:
                                st.metric("标准格式", stats['standard_format_count'])
                            with col4:
                                st.metric("重名冲突", stats['conflict_count'])

                        else:
                            st.error(f"预览失败: {preview_result['message']}")

                    except Exception as e:
                        st.error(f"预览失败: {str(e)}")

            # 处理执行请求
            if execute_submitted:
                # 验证抖音号设置
                if aweme_setting_option == "指定统一的抖音号":
                    if not target_aweme_id:
                        st.error("⚠️ 请输入目标抖音号")
                        st.stop()
                    elif not target_aweme_id.isdigit():
                        st.error("⚠️ 抖音号必须是纯数字格式")
                        st.stop()

                # 保存抖音号设置
                st.session_state.batch_aweme_setting = {
                    'option': aweme_setting_option,
                    'target_aweme_id': target_aweme_id if aweme_setting_option == "指定统一的抖音号" else None
                }

                # 启动批量复制任务
                st.session_state.batch_task_running = True
                st.session_state.batch_task_paused = False

                # 计算总任务数（每个计划在其源账户内复制）
                total_tasks = len(st.session_state.batch_selected_campaigns)
                st.session_state.batch_progress = {
                    'current': 0,
                    'total': total_tasks,
                    'logs': [],
                    'success_count': 0,
                    'failed_count': 0
                }

                st.rerun()

        # 任务执行状态显示
        if st.session_state.batch_task_running:
            st.subheader("🎯 批量复制任务执行中")

            # 任务控制按钮
            col1, col2, col3 = st.columns(3)

            with col1:
                if st.session_state.batch_task_paused:
                    if st.button("▶️ 继续任务", use_container_width=True):
                        st.session_state.batch_task_paused = False
                        st.rerun()
                else:
                    if st.button("⏸️ 暂停任务", use_container_width=True):
                        st.session_state.batch_task_paused = True
                        st.rerun()

            with col2:
                if st.button("🛑 结束任务", use_container_width=True):
                    st.session_state.batch_task_running = False
                    st.session_state.batch_task_paused = False
                    st.warning("⚠️ 任务已被用户手动结束")
                    st.rerun()

            with col3:
                task_status = "⏸️ 已暂停" if st.session_state.batch_task_paused else "🔄 运行中"
                st.info(f"任务状态: {task_status}")

            # 进度显示
            progress = st.session_state.batch_progress
            if progress['total'] > 0:
                progress_percentage = progress['current'] / progress['total']
                st.progress(progress_percentage, text=f"进度: {progress['current']}/{progress['total']} ({progress_percentage:.1%})")

                # 统计信息
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("已完成", progress['current'])
                with col2:
                    st.metric("成功", progress['success_count'])
                with col3:
                    st.metric("失败", progress['failed_count'])

            # 实时日志显示
            if progress['logs']:
                st.subheader("📋 执行日志")
                log_container = st.container()
                with log_container:
                    # 显示最近的10条日志
                    recent_logs = progress['logs'][-10:]
                    for log_entry in recent_logs:
                        if log_entry['type'] == 'success':
                            st.success(f"✅ {log_entry['message']}")
                        elif log_entry['type'] == 'error':
                            st.error(f"❌ {log_entry['message']}")
                        else:
                            st.info(f"ℹ️ {log_entry['message']}")

            # 执行批量复制逻辑（如果任务运行中且未暂停）
            if not st.session_state.batch_task_paused:
                # 获取抖音号设置
                aweme_setting = st.session_state.get('batch_aweme_setting', {})
                execute_batch_replication_task(
                    new_plan_prefix,
                    clean_prefixes,
                    preserve_standard_format,
                    aweme_setting
                )

def execute_batch_replication_task(new_plan_prefix, clean_prefixes, preserve_standard_format, aweme_setting=None):
    """执行批量复制任务"""
    try:
        # 检查任务是否应该继续
        if not st.session_state.batch_task_running or st.session_state.batch_task_paused:
            return

        progress = st.session_state.batch_progress

        # 如果任务刚开始，添加开始日志
        if progress['current'] == 0:
            progress['logs'].append({
                'type': 'info',
                'message': f"开始批量账户内复制任务：{len(st.session_state.batch_selected_campaigns)} 个计划"
            })

        # 按源账户分组处理计划
        campaigns_by_account = {}
        for campaign in st.session_state.batch_selected_campaigns:
            account_id = campaign['source_account_id']
            if account_id not in campaigns_by_account:
                campaigns_by_account[account_id] = []
            campaigns_by_account[account_id].append(campaign)

        # 执行批量复制
        with st.spinner("正在执行批量账户内复制..."):
            try:
                total_success = 0
                total_failed = 0

                # 为每个源账户执行复制
                for account_id, campaigns in campaigns_by_account.items():
                    progress['logs'].append({
                        'type': 'info',
                        'message': f"正在处理账户 {account_id} 的 {len(campaigns)} 个计划"
                    })

                    # 在源账户内复制（目标账户ID与源账户ID相同）
                    result = replicate_plan_tool.run_replication(
                        target_account_ids=[account_id],  # 目标账户就是源账户
                        new_plan_prefix=new_plan_prefix if new_plan_prefix else None,
                        selected_campaigns=campaigns,
                        clean_prefixes=clean_prefixes,
                        preserve_standard_format=preserve_standard_format,
                        aweme_setting=aweme_setting
                    )

                    if result['success']:
                        total_success += len(campaigns)
                        progress['logs'].append({
                            'type': 'success',
                            'message': f"✅ 账户 {account_id} 复制成功"
                        })
                    else:
                        total_failed += len(campaigns)
                        progress['logs'].append({
                            'type': 'error',
                            'message': f"❌ 账户 {account_id} 复制失败: {result['message']}"
                        })

                # 汇总结果
                if total_success > 0:
                    final_result = {
                        'success': True,
                        'message': f"批量复制完成: {total_success} 个成功, {total_failed} 个失败"
                    }
                else:
                    final_result = {
                        'success': False,
                        'message': f"所有复制操作都失败了"
                    }

                if final_result['success']:
                    # 任务成功完成
                    st.session_state.batch_task_running = False
                    progress['current'] = progress['total']
                    progress['logs'].append({
                        'type': 'success',
                        'message': f"✅ 批量复制任务完成: {final_result['message']}"
                    })

                    st.success(f"🎉 批量账户内复制任务完成！{final_result['message']}")

                    # 显示完成后的操作选项
                    col1, col2 = st.columns(2)
                    with col1:
                        if st.button("🔄 开始新的批量复制"):
                            # 重置状态
                            st.session_state.batch_replication_step = 1
                            st.session_state.batch_found_campaigns = []
                            st.session_state.batch_selected_campaigns = []
                            st.session_state.batch_source_accounts = []
                            st.session_state.batch_task_running = False
                            st.session_state.batch_task_paused = False
                            st.session_state.batch_progress = {'current': 0, 'total': 0, 'logs': []}
                            st.rerun()

                    with col2:
                        if st.button("📊 查看详细结果"):
                            st.info("详细结果已记录在执行日志中")
                else:
                    # 任务失败
                    st.session_state.batch_task_running = False
                    progress['logs'].append({
                        'type': 'error',
                        'message': f"❌ 批量复制任务失败: {final_result['message']}"
                    })
                    st.error(f"❌ 批量账户内复制失败: {final_result['message']}")

            except Exception as e:
                # 执行异常
                st.session_state.batch_task_running = False
                progress['logs'].append({
                    'type': 'error',
                    'message': f"❌ 执行异常: {str(e)}"
                })
                st.error(f"执行批量复制失败: {str(e)}")

    except Exception as e:
        st.error(f"批量复制任务执行器异常: {str(e)}")

def render_harvest_and_relaunch():
    """渲染优质素材收割与再投放功能"""
    st.info("自动发现当前账户在某个时间范围内的优质素材，并用这些素材快速创建一批新计划。")

    # 检查是否选择了账户
    current_harvest_account = require_account_selection("请先在左侧栏选择一个广告账户进行素材收割")

    with st.form("harvest_form"):
            
            today = datetime.now()
            col1_harvest, col2_harvest = st.columns(2)
            with col1_harvest:
                start_date = st.date_input("收割开始日期", today - timedelta(days=7))
            with col2_harvest:
                end_date = st.date_input("收割结束日期", today)

            submitted_harvest = st.form_submit_button("开始收割与再投放")
            if submitted_harvest:
                advertiser_id = current_harvest_account.account_id_qc
                principal_id = current_harvest_account.principal_id

                # 安全地处理日期格式化
                from datetime import date

                if isinstance(start_date, date):
                    start_date_str = start_date.strftime('%Y-%m-%d')
                elif isinstance(start_date, tuple) and len(start_date) > 0 and isinstance(start_date[0], date):
                    start_date_str = start_date[0].strftime('%Y-%m-%d')
                else:
                    start_date_str = str(start_date)

                if isinstance(end_date, date):
                    end_date_str = end_date.strftime('%Y-%m-%d')
                elif isinstance(end_date, tuple) and len(end_date) > 0 and isinstance(end_date[0], date):
                    end_date_str = end_date[0].strftime('%Y-%m-%d')
                else:
                    end_date_str = str(end_date)

                kwargs = {
                    "advertiser_id": int(advertiser_id),
                    "principal_id": int(principal_id),
                    "start_date": start_date_str,
                    "end_date": end_date_str,
                }
                with st.spinner(f"正在收割账户 {current_harvest_account.name} ({advertiser_id}) 的优质素材..."):
                    run_with_log_capture(harvest_tool.run_harvest_and_relaunch, **kwargs)

def render_admin_tools_page():
    """系统管理工具页面 - System Management Tools Page"""
    st.header("🛠️ 系统管理工具")
    st.markdown("系统维护和管理功能集合 - System maintenance and management functions")

    # 僵尸素材管理 - Zombie Materials Management
    with st.expander("🧟 僵尸素材管理 (Zombie Materials Management)", expanded=True):
        st.info("管理卡在 'processing' 状态的僵尸素材 - Manage materials stuck in 'processing' status")
        st.markdown("**功能说明**: 检测和重置长时间卡在处理状态的素材，避免工作流阻塞")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("📊 检查素材状态 (Check Material Status)", use_container_width=True):
                try:
                    from qianchuan_aw.utils.db_utils import database_session
                    from qianchuan_aw.database.models import LocalCreative
                    from sqlalchemy import func
                    from datetime import datetime, timedelta

                    with database_session() as db:
                        # 素材状态分布
                        status_counts = db.query(
                            LocalCreative.status,
                            func.count(LocalCreative.id).label('count')
                        ).group_by(LocalCreative.status).all()

                        if status_counts:
                            status_data = []
                            total = 0
                            for status, count in status_counts:
                                status_data.append({
                                    "状态": translate_status(status),
                                    "英文状态": status,
                                    "数量": count
                                })
                                total += count

                            st.dataframe(status_data, use_container_width=True)
                            st.metric("总素材数", total)

                            # 检查僵尸素材
                            cutoff_time = datetime.now() - timedelta(minutes=10)
                            zombie_count = db.query(LocalCreative).filter(
                                LocalCreative.status == 'processing',
                                LocalCreative.updated_at < cutoff_time
                            ).count()

                            if zombie_count > 0:
                                st.warning(f"⚠️ 发现 {zombie_count} 个僵尸素材（处理超过10分钟）")
                            else:
                                st.success("✅ 没有发现僵尸素材")
                        else:
                            st.info("没有找到素材记录")

                except Exception as e:
                    st.error(f"检查失败: {e}")

        with col2:
            if st.button("🔄 重置僵尸素材 (Reset Zombie Materials)", use_container_width=True):
                try:
                    from qianchuan_aw.utils.db_utils import database_session
                    from qianchuan_aw.database.models import LocalCreative
                    from datetime import datetime, timedelta

                    with database_session() as db:
                        cutoff_time = datetime.now() - timedelta(minutes=5)
                        zombie_materials = db.query(LocalCreative).filter(
                            LocalCreative.status == 'processing',
                            LocalCreative.updated_at < cutoff_time
                        ).all()

                        if zombie_materials:
                            reset_count = 0
                            for material in zombie_materials:
                                material.status = 'pending_grouping'
                                reset_count += 1

                            db.commit()
                            st.success(f"✅ 成功重置 {reset_count} 个僵尸素材")
                        else:
                            st.info("没有发现需要重置的僵尸素材")

                except Exception as e:
                    st.error(f"重置失败: {e}")

        with col3:
            if st.button("⚠️ 强制重置所有", use_container_width=True):
                if st.session_state.get('confirm_force_reset', False):
                    try:
                        from qianchuan_aw.utils.db_utils import database_session
                        from qianchuan_aw.database.models import LocalCreative

                        with database_session() as db:
                            processing_materials = db.query(LocalCreative).filter(
                                LocalCreative.status == 'processing'
                            ).all()

                            if processing_materials:
                                reset_count = 0
                                for material in processing_materials:
                                    material.status = 'pending_grouping'
                                    reset_count += 1

                                db.commit()
                                st.success(f"✅ 强制重置 {reset_count} 个素材")
                                st.session_state['confirm_force_reset'] = False
                            else:
                                st.info("没有processing状态的素材")

                    except Exception as e:
                        st.error(f"强制重置失败: {e}")
                else:
                    st.warning("⚠️ 这将重置所有processing状态的素材，请再次点击确认")
                    st.session_state['confirm_force_reset'] = True

    # 工作流状态监控
    with st.expander("📊 工作流任务状态", expanded=True):
        st.info("查看所有工作流任务的配置和状态")

        try:
            from qianchuan_aw.utils.config_manager import get_config_manager
            config_manager = get_config_manager()
            app_settings = config_manager.get_config()
            workflow_config = app_settings.get('workflow', {})

            # 任务配置表格
            task_configs = [
                {
                    "任务名称": "文件摄取",
                    "间隔": f"{workflow_config.get('file_ingestion', {}).get('interval_seconds', 300)}秒",
                    "状态": "✅ 启用" if workflow_config.get('file_ingestion', {}).get('enabled', True) else "❌ 禁用",
                    "功能": "扫描并入库新素材"
                },
                {
                    "任务名称": "分组派发",
                    "间隔": f"{workflow_config.get('group_dispatch', {}).get('interval_seconds', 60)}秒",
                    "状态": "✅ 启用" if workflow_config.get('group_dispatch', {}).get('enabled', True) else "❌ 禁用",
                    "功能": "将素材分组并派发上传"
                },
                {
                    "任务名称": "计划创建",
                    "间隔": f"{workflow_config.get('plan_creation', {}).get('interval_seconds', 120)}秒",
                    "状态": "✅ 启用" if workflow_config.get('plan_creation', {}).get('enabled', True) else "❌ 禁用",
                    "功能": "为已上传素材创建测试计划"
                },
                {
                    "任务名称": "计划提审",
                    "间隔": f"{workflow_config.get('plan_appeal', {}).get('interval_seconds', 180)}秒",
                    "状态": "✅ 启用" if workflow_config.get('plan_appeal', {}).get('enabled', True) else "❌ 禁用",
                    "功能": "自动提审待审核计划"
                },
                {
                    "任务名称": "素材监控",
                    "间隔": f"{workflow_config.get('material_monitoring', {}).get('interval_seconds', 300)}秒",
                    "状态": "✅ 启用" if workflow_config.get('material_monitoring', {}).get('enabled', True) else "❌ 禁用",
                    "功能": "监控素材审核状态和性能"
                },
                {
                    "任务名称": "评论管理",
                    "间隔": f"{workflow_config.get('comment_management', {}).get('check_interval_minutes', 5)}分钟",
                    "状态": "✅ 启用" if workflow_config.get('comment_management', {}).get('enabled', True) else "❌ 禁用",
                    "功能": "屏蔽负面评论"
                },
                {
                    "任务名称": "违规检测",
                    "间隔": f"{workflow_config.get('violation_detection', {}).get('check_interval_minutes', 30)}分钟",
                    "状态": "✅ 启用" if workflow_config.get('violation_detection', {}).get('enabled', True) else "❌ 禁用",
                    "功能": "检查账户违规和扣分"
                },
                {
                    "任务名称": "僵尸清理",
                    "间隔": f"{workflow_config.get('zombie_cleanup', {}).get('patrol_interval_minutes', 10)}分钟",
                    "状态": "✅ 启用",
                    "功能": "清理卡住的处理任务"
                },
                {
                    "任务名称": "素材收集",
                    "间隔": f"{workflow_config.get('material_collection', {}).get('interval_minutes', 60)}分钟",
                    "状态": "✅ 启用" if workflow_config.get('material_collection', {}).get('enabled', True) else "❌ 禁用",
                    "功能": "从局域网收集视频素材"
                }
            ]

            st.dataframe(task_configs, use_container_width=True)

            # 配置提示
            st.info("💡 可在 `config/settings.yml` 中修改任务配置，修改后需重启Celery服务")

        except Exception as e:
            st.error(f"获取工作流配置失败: {e}")

    # 工作流控制
    with st.expander("🎛️ 工作流控制"):
        st.info("控制工作流任务的启动、暂停和停止")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("▶️ 启动所有任务", use_container_width=True):
                try:
                    import subprocess
                    import os

                    # 检查是否已有Celery进程运行
                    result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'],
                                          capture_output=True, text=True, shell=True)

                    if 'celery' in result.stdout.lower():
                        st.warning("⚠️ 检测到Celery进程可能已在运行，请先停止现有进程")
                    else:
                        st.info("🚀 正在启动Celery服务...")
                        st.code("""
请在命令行中执行以下命令启动工作流：

# 启动Worker
celery -A qianchuan_aw.celery_app worker --loglevel=info --pool=solo

# 启动Beat（新开一个命令行窗口）
celery -A qianchuan_aw.celery_app beat --loglevel=info
                        """)

                except Exception as e:
                    st.error(f"启动失败: {e}")

        with col2:
            if st.button("⏸️ 暂停任务", use_container_width=True):
                try:
                    # 通过修改配置文件暂停所有任务
                    from qianchuan_aw.utils.config_manager import get_config_manager
                    import yaml

                    config_manager = get_config_manager()
                    config_path = config_manager._config_file_path

                    # 读取当前配置
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = yaml.safe_load(f)

                    # 暂停所有任务
                    workflow_tasks = ['file_ingestion', 'group_dispatch', 'plan_creation',
                                    'plan_appeal', 'material_monitoring', 'comment_management',
                                    'violation_detection']

                    paused_count = 0
                    for task in workflow_tasks:
                        if config.get('workflow', {}).get(task, {}).get('enabled', True):
                            if 'workflow' not in config:
                                config['workflow'] = {}
                            if task not in config['workflow']:
                                config['workflow'][task] = {}
                            config['workflow'][task]['enabled'] = False
                            paused_count += 1

                    # 保存配置
                    with open(config_path, 'w', encoding='utf-8') as f:
                        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)

                    # 重新加载配置
                    config_manager.reload_config()

                    st.success(f"✅ 已暂停 {paused_count} 个任务，配置已保存")
                    st.warning("⚠️ 需要重启Celery服务使配置生效")

                except Exception as e:
                    st.error(f"暂停失败: {e}")

        with col3:
            if st.button("🛑 停止服务", use_container_width=True):
                try:
                    import subprocess

                    st.info("🛑 正在尝试停止Celery进程...")

                    # Windows下停止Celery进程
                    try:
                        # 查找并终止celery进程
                        result = subprocess.run(['taskkill', '/F', '/IM', 'python.exe', '/FI', 'WINDOWTITLE eq celery*'],
                                              capture_output=True, text=True, shell=True)

                        if result.returncode == 0:
                            st.success("✅ Celery进程已停止")
                        else:
                            st.warning("⚠️ 未找到运行中的Celery进程，或停止失败")

                    except Exception as e:
                        st.error(f"停止进程失败: {e}")

                    st.info("💡 也可以在运行Celery的命令行窗口中按 Ctrl+C 停止服务")

                except Exception as e:
                    st.error(f"停止失败: {e}")

        # 恢复任务
        st.subheader("🔄 任务恢复")
        if st.button("🔄 恢复所有任务", use_container_width=True):
            try:
                from qianchuan_aw.utils.config_manager import get_config_manager
                import yaml

                config_manager = get_config_manager()
                config_path = config_manager._config_file_path

                # 读取当前配置
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)

                # 恢复所有任务
                workflow_tasks = ['file_ingestion', 'group_dispatch', 'plan_creation',
                                'plan_appeal', 'material_monitoring', 'comment_management',
                                'violation_detection']

                resumed_count = 0
                for task in workflow_tasks:
                    if not config.get('workflow', {}).get(task, {}).get('enabled', True):
                        if 'workflow' not in config:
                            config['workflow'] = {}
                        if task not in config['workflow']:
                            config['workflow'][task] = {}
                        config['workflow'][task]['enabled'] = True
                        resumed_count += 1

                # 保存配置
                with open(config_path, 'w', encoding='utf-8') as f:
                    yaml.dump(config, f, default_flow_style=False, allow_unicode=True)

                # 重新加载配置
                config_manager.reload_config()

                st.success(f"✅ 已恢复 {resumed_count} 个任务，配置已保存")
                st.warning("⚠️ 需要重启Celery服务使配置生效")

            except Exception as e:
                st.error(f"恢复失败: {e}")

        # 单个任务控制
        st.subheader("🎯 单个任务控制")

        task_names = {
            'material_collection': '素材收集',
            'file_ingestion': '文件摄取',
            'group_dispatch': '分组派发',
            'plan_creation': '计划创建',
            'plan_appeal': '计划提审',
            'material_monitoring': '素材监控',
            'comment_management': '评论管理',
            'violation_detection': '违规检测'
        }

        selected_task = st.selectbox("选择要控制的任务",
                                   options=list(task_names.keys()),
                                   format_func=lambda x: task_names[x])

        col1, col2 = st.columns(2)

        with col1:
            if st.button(f"⏸️ 暂停 {task_names[selected_task]}", use_container_width=True):
                try:
                    from qianchuan_aw.utils.config_manager import get_config_manager
                    import yaml

                    config_manager = get_config_manager()
                    config_path = config_manager._config_file_path

                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = yaml.safe_load(f)

                    if 'workflow' not in config:
                        config['workflow'] = {}
                    if selected_task not in config['workflow']:
                        config['workflow'][selected_task] = {}

                    config['workflow'][selected_task]['enabled'] = False

                    with open(config_path, 'w', encoding='utf-8') as f:
                        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)

                    config_manager.reload_config()
                    st.success(f"✅ 已暂停 {task_names[selected_task]} 任务")

                except Exception as e:
                    st.error(f"暂停失败: {e}")

        with col2:
            if st.button(f"▶️ 启用 {task_names[selected_task]}", use_container_width=True):
                try:
                    from qianchuan_aw.utils.config_manager import get_config_manager
                    import yaml

                    config_manager = get_config_manager()
                    config_path = config_manager._config_file_path

                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = yaml.safe_load(f)

                    if 'workflow' not in config:
                        config['workflow'] = {}
                    if selected_task not in config['workflow']:
                        config['workflow'][selected_task] = {}

                    config['workflow'][selected_task]['enabled'] = True

                    with open(config_path, 'w', encoding='utf-8') as f:
                        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)

                    config_manager.reload_config()
                    st.success(f"✅ 已启用 {task_names[selected_task]} 任务")

                except Exception as e:
                    st.error(f"启用失败: {e}")

        # 服务状态检查
        st.subheader("📊 服务状态")

        try:
            import subprocess

            # 检查Celery进程状态
            result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'],
                                  capture_output=True, text=True, shell=True)

            celery_processes = []
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'python.exe' in line and len(line.split()) >= 2:
                        celery_processes.append(line.strip())

            if celery_processes:
                st.success(f"✅ 检测到 {len(celery_processes)} 个Python进程运行中")

                # 尝试检查Redis连接
                try:
                    import redis
                    from qianchuan_aw.utils.config_manager import get_config_manager

                    config_manager = get_config_manager()
                    redis_config = config_manager.get_redis_config()

                    r = redis.Redis(
                        host=redis_config.get('host', 'localhost'),
                        port=redis_config.get('port', 6379),
                        db=redis_config.get('db', 0),
                        socket_timeout=2
                    )
                    r.ping()
                    st.success("✅ Redis连接正常")

                except Exception as e:
                    st.error(f"❌ Redis连接失败: {e}")

            else:
                st.warning("⚠️ 未检测到Celery进程运行")

        except Exception as e:
            st.error(f"状态检查失败: {e}")

    # 账户管理工具
    with st.expander("🏢 账户管理工具"):
        st.info("快速修复账户配置问题")

        if st.button("🔧 检查并修复测试账户"):
            try:
                from qianchuan_aw.utils.db_utils import database_session
                from qianchuan_aw.database.models import AdAccount

                with database_session() as db:
                    # 检查测试账户
                    test_accounts = db.query(AdAccount).filter(
                        AdAccount.account_type == 'TEST',
                        AdAccount.status == 'active'
                    ).all()

                    if test_accounts:
                        st.success(f"✅ 找到 {len(test_accounts)} 个活跃测试账户")
                        for acc in test_accounts:
                            st.write(f"- {acc.name} (ID: {acc.account_id_qc})")
                    else:
                        st.warning("❌ 没有活跃测试账户，尝试修复...")

                        # 查找第一个活跃账户并设置为测试账户
                        active_accounts = db.query(AdAccount).filter(
                            AdAccount.status == 'active'
                        ).all()

                        if active_accounts:
                            first_account = active_accounts[0]
                            first_account.account_type = 'TEST'
                            db.commit()
                            st.success(f"✅ 已将账户 '{first_account.name}' 设置为测试账户")
                        else:
                            # 激活第一个账户
                            all_accounts = db.query(AdAccount).all()
                            if all_accounts:
                                first_account = all_accounts[0]
                                first_account.status = 'active'
                                first_account.account_type = 'TEST'
                                db.commit()
                                st.success(f"✅ 已激活账户 '{first_account.name}' 并设置为测试账户")
                            else:
                                st.error("❌ 数据库中没有任何账户")

            except Exception as e:
                st.error(f"修复失败: {e}")


def render_debug_page():
    """调试页面 - 查看系统状态"""
    st.header("🔍 系统调试与状态检查")

    with st.expander("数据库状态检查", expanded=True):
        if st.button("🔄 刷新状态"):
            try:
                from qianchuan_aw.utils.db_utils import database_session
                from qianchuan_aw.database.models import LocalCreative, AdAccount, Principal
                from sqlalchemy import func

                with database_session() as db:
                    # 素材状态分布
                    st.subheader("📊 素材状态分布")
                    status_counts = db.query(
                        LocalCreative.status,
                        func.count(LocalCreative.id).label('count')
                    ).group_by(LocalCreative.status).all()

                    if status_counts:
                        status_data = []
                        total_materials = 0
                        for status, count in status_counts:
                            status_data.append({"状态": status, "数量": count})
                            total_materials += count

                        st.dataframe(status_data, use_container_width=True)
                        st.metric("总素材数", total_materials)
                    else:
                        st.info("没有找到素材记录")

                    # 测试账户检查
                    st.subheader("🏢 测试账户状态")
                    test_accounts = db.query(AdAccount).filter(
                        AdAccount.account_type == 'TEST',
                        AdAccount.status == 'active'
                    ).all()

                    if test_accounts:
                        account_data = []
                        for acc in test_accounts:
                            account_data.append({
                                "账户名": acc.name,
                                "千川ID": acc.account_id_qc,
                                "状态": acc.status
                            })
                        st.dataframe(account_data, use_container_width=True)
                        st.success(f"✅ 找到 {len(test_accounts)} 个活跃测试账户")
                    else:
                        st.error("❌ 没有找到活跃的测试账户！这可能是问题的根源。")

                    # 待分组素材详情
                    st.subheader("📋 待分组素材详情")
                    pending_materials = db.query(LocalCreative).filter(
                        LocalCreative.status == 'pending_grouping'
                    ).limit(10).all()

                    if pending_materials:
                        pending_data = []
                        for material in pending_materials:
                            file_name = os.path.basename(material.file_path) if material.file_path else "未知"
                            pending_data.append({
                                "ID": material.id,
                                "文件名": file_name,
                                "创建时间": material.created_at.strftime("%Y-%m-%d %H:%M:%S") if material.created_at else "未知"
                            })
                        st.dataframe(pending_data, use_container_width=True)

                        total_pending = db.query(LocalCreative).filter(
                            LocalCreative.status == 'pending_grouping'
                        ).count()
                        st.info(f"总共有 {total_pending} 个待分组素材")
                    else:
                        st.warning("⚠️ 没有待分组的素材")

                    # 处理中的素材
                    st.subheader("🔄 处理中的素材")
                    processing_materials = db.query(LocalCreative).filter(
                        LocalCreative.status == 'processing'
                    ).all()

                    if processing_materials:
                        st.warning(f"⚠️ 发现 {len(processing_materials)} 个处理中的素材，可能存在卡住的任务")
                        for material in processing_materials:
                            file_name = os.path.basename(material.file_path) if material.file_path else "未知"
                            time_diff = datetime.now() - material.updated_at if material.updated_at else "未知"
                            st.write(f"- {file_name} (处理时长: {time_diff})")
                    else:
                        st.success("✅ 没有处理中的素材")

            except Exception as e:
                st.error(f"检查失败: {e}")
                st.exception(e)


def render_smart_appeal_scheduler_page():
    """智能提审调度器页面"""
    try:
        # 导入简化版智能提审调度器页面
        sys.path.insert(0, os.path.join(project_root, 'src', 'qianchuan_aw', 'web_ui', 'pages'))
        from simple_smart_appeal_scheduler import main as smart_scheduler_main

        # 运行智能提审调度器页面
        smart_scheduler_main()

    except ImportError as e:
        st.error(f"❌ 智能提审调度器模块导入失败: {e}")
        st.info("请确保智能提审调度器模块已正确安装")

        # 提供备用的基础界面
        st.header("🚀 智能提审调度器")
        st.warning("⚠️ 模块加载失败，显示基础界面")

        if st.button("🔧 尝试手动执行"):
            st.info("请使用命令行执行: python ai_tools/optimization/ai_tool_20250728_optimization_simple_smart_scheduler.py")

    except Exception as e:
        st.error(f"❌ 智能提审调度器运行失败: {e}")

        # 显示错误详情
        with st.expander("查看错误详情"):
            st.code(str(e))

        # 提供解决建议
        st.info("💡 解决建议:")
        st.info("1. 检查配置文件是否正确")
        st.info("2. 确保数据库连接正常")
        st.info("3. 检查智能调度器模块是否完整")

def render_operation_tools_page():
    st.header("🛠️ 运营与数据工具")

    # 抖音号授权功能
    with st.expander("🎯 抖音号授权管理", expanded=True):
        st.info("为指定的广告户批量授权抖音号，授权后数据库中对应广告户的抖音号会自动更新")

        # 获取所有广告账户
        try:
            with database_session() as db:
                accounts = db.query(AdAccount).options(
                    joinedload(AdAccount.principal)
                ).filter(AdAccount.status == 'active').all()

                if not accounts:
                    st.warning("没有找到可用的广告账户")
                else:
                    # ⭐ 账户筛选与收藏管理区域
                    st.markdown("#### ⭐ 账户筛选与收藏管理")
                    col1, col2, col3 = st.columns([2, 1, 1])

                    with col1:
                        # 筛选选项
                        filter_option = st.selectbox(
                            "账户筛选",
                            ["显示全部账户", "仅显示收藏账户", "仅显示未收藏账户"],
                            key="account_filter_option"
                        )

                    with col2:
                        # 统计信息
                        favorite_count = len([acc for acc in accounts if getattr(acc, 'is_favorite', False)])
                        st.metric("收藏账户", f"{favorite_count}/{len(accounts)}")

                    with col3:
                        # 收藏管理按钮
                        if st.button("🛠️ 管理收藏", key="manage_favorites_btn"):
                            st.session_state.show_favorite_manager = True

                    # 批量收藏管理界面
                    if st.session_state.get('show_favorite_manager', False):
                        st.markdown("#### 📝 批量收藏管理")

                        # 创建收藏状态编辑界面
                        favorite_changes = {}

                        for account in accounts:
                            col_check, col_name = st.columns([1, 4])
                            current_favorite = getattr(account, 'is_favorite', False)

                            with col_check:
                                new_favorite = st.checkbox(
                                    "⭐" if current_favorite else "☆",
                                    value=current_favorite,
                                    key=f"fav_{account.id}",
                                    help="点击切换收藏状态"
                                )

                            with col_name:
                                aweme_status = f"[已授权: {account.aweme_id}]" if account.aweme_id else "[未授权]"
                                st.write(f"{account.name} ({account.account_id_qc}) - {account.principal.name} {aweme_status}")

                            if new_favorite != current_favorite:
                                favorite_changes[account.id] = new_favorite

                        # 保存收藏更改
                        col_save, col_cancel = st.columns(2)
                        with col_save:
                            if st.button("💾 保存更改", key="save_favorites"):
                                if favorite_changes:
                                    try:
                                        for account_id, is_favorite in favorite_changes.items():
                                            db.query(AdAccount).filter(AdAccount.id == account_id).update(
                                                {"is_favorite": is_favorite}
                                            )
                                        db.commit()
                                        st.success(f"✅ 已更新 {len(favorite_changes)} 个账户的收藏状态")
                                        st.session_state.show_favorite_manager = False
                                        st.rerun()
                                    except Exception as e:
                                        st.error(f"❌ 保存失败: {e}")
                                else:
                                    st.info("没有需要保存的更改")

                        with col_cancel:
                            if st.button("❌ 取消", key="cancel_favorites"):
                                st.session_state.show_favorite_manager = False
                                st.rerun()

                    # 根据筛选条件过滤账户
                    if filter_option == "仅显示收藏账户":
                        filtered_accounts = [acc for acc in accounts if getattr(acc, 'is_favorite', False)]
                    elif filter_option == "仅显示未收藏账户":
                        filtered_accounts = [acc for acc in accounts if not getattr(acc, 'is_favorite', False)]
                    else:
                        filtered_accounts = accounts

                    # 按收藏状态排序（收藏的在前）
                    filtered_accounts.sort(key=lambda x: (not getattr(x, 'is_favorite', False), x.name))

                    # 创建账户选择选项（带收藏标识）
                    account_options = {}
                    for account in filtered_accounts:
                        is_favorite = getattr(account, 'is_favorite', False)
                        star_icon = "⭐ " if is_favorite else ""
                        aweme_status = f" [已授权]" if account.aweme_id else ""
                        display_name = f"{star_icon}{account.name} ({account.account_id_qc}) - {account.principal.name}{aweme_status}"
                        account_options[display_name] = account

                    with st.form("aweme_auth_form"):
                        st.markdown("#### 📋 选择广告账户")
                        st.info(f"📊 当前筛选显示 {len(filtered_accounts)} 个账户（共 {len(accounts)} 个）")

                        selected_accounts = st.multiselect(
                            "选择要授权的广告账户",
                            options=list(account_options.keys()),
                            help="⭐ 标记表示收藏账户，可以选择多个广告账户进行批量授权"
                        )

                        st.markdown("#### 🎬 抖音号信息")
                        col1, col2 = st.columns(2)

                        with col1:
                            aweme_uid = st.text_input(
                                "抖音号UID",
                                placeholder="例如：**************",
                                help="抖音号的唯一标识符（UID），不是抖音号本身"
                            )

                        with col2:
                            auth_code = st.text_input(
                                "合作码",
                                placeholder="例如：***********",
                                help="抖音号的授权合作码"
                            )

                        st.markdown("#### ⚙️ 授权选项")
                        update_database = st.checkbox(
                            "授权成功后自动更新数据库中的抖音号",
                            value=True,
                            help="勾选后，授权成功的账户会自动更新其关联的抖音号UID（覆盖原有记录）"
                        )

                        submitted = st.form_submit_button("🚀 开始授权", use_container_width=True)

                        if submitted:
                            if not selected_accounts:
                                st.error("❌ 请至少选择一个广告账户")
                            elif not aweme_uid or not auth_code:
                                st.error("❌ 抖音号UID和合作码都不能为空")
                            else:
                                # 执行授权
                                selected_account_objects = [account_options[name] for name in selected_accounts]

                                with st.spinner(f"正在为 {len(selected_account_objects)} 个账户授权抖音号..."):
                                    success_count = 0
                                    failure_count = 0

                                    # 按主体分组处理
                                    from collections import defaultdict
                                    principal_groups = defaultdict(list)
                                    for account in selected_account_objects:
                                        principal_groups[account.principal_id].append(account)

                                    # 加载API配置
                                    app_settings = load_settings()
                                    app_id = app_settings['api_credentials']['app_id']
                                    secret = app_settings['api_credentials']['secret']

                                    # 创建结果容器
                                    results_container = st.container()

                                    for principal_id, accounts_in_principal in principal_groups.items():
                                        principal_name = accounts_in_principal[0].principal.name

                                        try:
                                            # 为当前主体初始化API客户端
                                            client = QianchuanClient(
                                                app_id=app_id,
                                                secret=secret,
                                                principal_id=principal_id
                                            )

                                            # 为主体下的每个账户执行授权
                                            for account in accounts_in_principal:
                                                try:
                                                    result = client.authorize_aweme_account(
                                                        advertiser_id=int(account.account_id_qc),
                                                        aweme_id=aweme_uid,
                                                        code=auth_code
                                                    )

                                                    if result and result.get("auth_success"):
                                                        success_count += 1

                                                        # 更新数据库中的抖音号
                                                        if update_database:
                                                            with database_session() as update_db:
                                                                db_account = update_db.query(AdAccount).filter(
                                                                    AdAccount.id == account.id
                                                                ).first()
                                                                if db_account:
                                                                    db_account.aweme_id = aweme_uid
                                                                    update_db.commit()

                                                        with results_container:
                                                            st.success(f"✅ {account.name} ({account.account_id_qc}) 授权成功")
                                                    else:
                                                        # 智能错误处理
                                                        error_message = result.get("message", "无详细错误信息") if result else "无返回结果"

                                                        # 检查是否是"已存在授权"错误
                                                        if "该授权类型已存在" in error_message or "不支持重复授权" in error_message:
                                                            # 对于已存在授权，视为成功并更新数据库
                                                            success_count += 1

                                                            if update_database:
                                                                with database_session() as update_db:
                                                                    db_account = update_db.query(AdAccount).filter(
                                                                        AdAccount.id == account.id
                                                                    ).first()
                                                                    if db_account:
                                                                        db_account.aweme_id = aweme_uid
                                                                        update_db.commit()

                                                            with results_container:
                                                                st.success(f"✅ {account.name} ({account.account_id_qc}) 授权确认（已存在授权）")
                                                        else:
                                                            failure_count += 1
                                                            with results_container:
                                                                if "No permission to operate account" in error_message:
                                                                    st.error(f"❌ {account.name} ({account.account_id_qc}) 权限不足，请检查Token权限")
                                                                else:
                                                                    st.error(f"❌ {account.name} ({account.account_id_qc}) 授权失败: {error_message}")

                                                except Exception as e:
                                                    # 检查异常信息中是否包含已存在授权的提示
                                                    error_str = str(e)
                                                    if "该授权类型已存在" in error_str or "不支持重复授权" in error_str:
                                                        success_count += 1

                                                        if update_database:
                                                            with database_session() as update_db:
                                                                db_account = update_db.query(AdAccount).filter(
                                                                    AdAccount.id == account.id
                                                                ).first()
                                                                if db_account:
                                                                    db_account.aweme_id = aweme_uid
                                                                    update_db.commit()

                                                        with results_container:
                                                            st.success(f"✅ {account.name} ({account.account_id_qc}) 授权确认（已存在授权）")
                                                    else:
                                                        failure_count += 1
                                                        with results_container:
                                                            if "No permission to operate account" in error_str:
                                                                st.error(f"❌ {account.name} ({account.account_id_qc}) 权限不足，请检查Token权限")
                                                            else:
                                                                st.error(f"❌ {account.name} ({account.account_id_qc}) 授权异常: {e}")

                                        except Exception as e:
                                            failure_count += len(accounts_in_principal)
                                            with results_container:
                                                st.error(f"❌ 主体 {principal_name} API客户端初始化失败: {e}")

                                    # 显示总结
                                    st.markdown("---")
                                    col_summary1, col_summary2, col_summary3 = st.columns(3)

                                    with col_summary1:
                                        st.metric("总处理数", len(selected_account_objects))
                                    with col_summary2:
                                        st.metric("成功数", success_count)
                                    with col_summary3:
                                        st.metric("失败数", failure_count)

                                    if success_count > 0:
                                        st.success(f"🎉 授权完成！成功授权 {success_count} 个账户")
                                        if update_database:
                                            st.info("✅ 数据库中的抖音号信息已自动更新（覆盖原有记录）")

                                    if failure_count > 0:
                                        st.warning(f"⚠️ 有 {failure_count} 个账户授权失败，请检查错误信息")

        except Exception as e:
            st.error(f"❌ 加载广告账户失败: {e}")

    with st.expander("批量添加评论屏蔽词"):
        st.info("为所有活跃账户添加在 `config/banned_terms.yml` 中定义的屏蔽词。")
        st.warning("⚠️ 请输入真正的抖音号（如：45153435301），不是数据库中的UID！")
        with st.form("banned_terms_form"):
            aweme_id_banned = st.text_input("抖音号", placeholder="例如：45153435301", help="输入真正的抖音号，系统会为所有活跃账户尝试添加屏蔽词")
            submitted_banned = st.form_submit_button("添加屏蔽词")
            if submitted_banned:
                if not aweme_id_banned:
                    st.error("抖音号不能为空！")
                else:
                    with st.spinner(f"正在为所有活跃账户添加屏蔽词（使用抖音号: {aweme_id_banned}）..."):
                        banned_terms_config = banned_terms_tool.load_banned_terms()
                        terms_to_add = banned_terms_config.get('banned_terms', [])
                        if not terms_to_add:
                            st.warning("配置文件 config/banned_terms.yml 中未找到任何屏蔽词。")
                        else:
                            st.info(f"将添加 {len(terms_to_add)} 个屏蔽词到所有活跃账户")

                            # 执行批量添加屏蔽词
                            try:
                                with st.spinner("正在为所有活跃账户添加屏蔽词..."):
                                    result = banned_terms_tool.add_banned_terms_to_accounts(aweme_id_banned)

                                # 显示结果
                                if result['success'] > 0:
                                    st.success(f"✅ {result['message']}")
                                    if result['failed'] > 0:
                                        st.warning(f"⚠️ 部分账户添加失败，通常是由于权限限制或抖音号未正确授权")
                                else:
                                    st.error(f"❌ {result['message']}")
                                    st.info("💡 常见失败原因：\n- 抖音号未授权给对应广告账户\n- API权限不足\n- 账户状态异常")

                                # 显示详细统计
                                col1, col2, col3, col4 = st.columns(4)
                                with col1:
                                    st.metric("✅ 成功", result['success'], delta=f"{result['success']/result['total']*100:.1f}%")
                                with col2:
                                    st.metric("❌ 失败", result['failed'], delta=f"{result['failed']/result['total']*100:.1f}%")
                                with col3:
                                    st.metric("📊 总计", result['total'])
                                with col4:
                                    st.metric("🎯 屏蔽词数", len(terms_to_add))

                                # 显示操作建议
                                if result['failed'] > 0:
                                    with st.expander("🔧 失败账户处理建议", expanded=False):
                                        st.markdown("""
                                        **常见解决方案：**
                                        1. **检查抖音号授权**：确保抖音号已正确授权给对应的广告账户
                                        2. **验证API权限**：确认API应用有操作该抖音号的权限
                                        3. **账户状态检查**：某些账户可能处于受限状态
                                        4. **重试操作**：部分失败可能是临时网络问题，可以稍后重试

                                        **权限错误说明：**
                                        - `40002: 您没有权限操作该抖音号` - 抖音号未授权或权限不足
                                        - 这是正常的API权限控制，不是系统错误
                                        """)

                            except Exception as e:
                                st.error(f"❌ 执行失败: {e}")
                                backend_logger.error(f"批量添加屏蔽词失败: {e}")

    with st.expander("同步广告报表"):
        st.info("一键同步所有账户、计划、素材昨日的报表数据。")
        if st.button("开始同步昨日报表"):
            with st.spinner("正在同步所有报表..."):
                run_with_log_capture(sync_reports_tool.run_sync_all_reports)

def render_snapshot_page():
    st.header("项目快照与备份")
    st.warning("快照功能依赖于系统环境中的 `pg_dump` 等工具，如果执行失败，请确保 PostgreSQL 客户端工具已安装并配置在系统的PATH中，或在 `config/settings.yml` 中指定 `postgres_bin_path`。")

    with st.form("create_snapshot_form"):
        st.subheader("创建新快照")
        message = st.text_input("快照描述", placeholder="例如：完成XX功能开发")
        submitted = st.form_submit_button("立即创建新快照")
        if submitted:
            if not message:
                st.error("快照描述不能为空！")
            else:
                with st.spinner("正在创建快照..."):
                    try:
                        snapshot_tool.create_snapshot(message)
                        st.success("快照创建成功！")
                        st.cache_data.clear()
                        st.rerun()
                    except Exception as e:
                        st.error(f"创建快照失败: {e}")

    st.subheader("历史快照列表")
    st.dataframe(get_all_snapshots(), use_container_width=True)
    st.info("从快照恢复的功能需要通过命令行 `tools/snapshot.py restore <name>` 执行。")

def render_settings_page():
    st.header("系统设置与授权")
    st.markdown("在此页面，您可以完成系统的基础配置，如添加业务主体、为API和抖音号授权等。")

    with st.expander("1. 添加新主体", expanded=True):
        st.info("注册一个新的业务主体，这是所有操作的起点。")
        with st.form("add_principal_form"):
            name = st.text_input("主体名称 (例如: XX公司)")
            cc_id = st.number_input("千川纵横ID", format="%d", value=0)
            submitted = st.form_submit_button("添加主体")
            if submitted:
                if not name or not cc_id:
                    st.error("主体名称和ID均不能为空！")
                else:
                    add_principal_tool.add_principal(name, int(cc_id))
                    st.success(f"主体 '{name}' 添加成功！")
                    st.cache_data.clear()
                    st.rerun()

    with st.expander("2. 主体API授权"):
        st.info("为已存在的主体获取千川API的访问权限。")
        principals = get_all_principals()
        if not principals:
            st.info("请先添加主体。")
        else:
            selected_principal = st.selectbox("选择要授权的主体", options=principals, key="auth_principal_select")
            settings = load_settings()
            app_id = settings['api_credentials']['app_id']
            auth_url = f"https://qianchuan.jinritemai.com/openapi/qc/audit/oauth.html?app_id={app_id}&state=streamlit_ui&material_auth=1&rid=zlg7tv6xqt"
            st.markdown(f"**授权链接:** [点击这里进行授权]({auth_url})")
            st.info("请点击上方链接，使用对应主体的千川纵横账号登录并授权，然后将跳转后的完整URL粘贴到下方。")

            redirect_url = st.text_input("粘贴跳转后的完整URL", "")
            if st.button("完成授权"):
                if not redirect_url:
                    st.error("请粘贴跳转后的URL。")
                else:
                    parsed_url = urlparse(redirect_url)
                    query_params = parse_qs(parsed_url.query)
                    auth_code = query_params.get('auth_code', [None])[0]
                    if not auth_code:
                        st.error("无法从您提供的URL中提取 'auth_code'。")
                    else:
                        with st.spinner("正在用 auth_code 换取 Token..."):
                            try:
                                with SessionLocal() as db:
                                    principal_obj = db.query(Principal).filter_by(name=selected_principal).first()
                                    if not principal_obj:
                                        st.error(f"找不到主体 {selected_principal}")
                                        return
                                    principal_id = principal_obj.id

                                client = QianchuanClient(app_id=app_id, secret=settings['api_credentials']['secret'], principal_id=principal_id)
                                token_data = client.get_access_token(auth_code)
                                st.success(f"主体 '{selected_principal}' 授权成功！")
                                st.json(token_data)
                            except Exception as e:
                                st.error(f"授权失败: {e}")

def render_material_analytics_page():
    """素材审核报表页面"""
    st.header("📊 素材审核报表")
    st.markdown("详细的素材审核数据分析，包括日期趋势、作者统计、审核通过率分析等")

    # 日期选择器
    col_date1, col_date2, col_date3 = st.columns(3)

    with col_date1:
        start_date = st.date_input(
            "开始日期",
            value=datetime.now().date() - timedelta(days=7),
            help="选择统计开始日期"
        )

    with col_date2:
        end_date = st.date_input(
            "结束日期",
            value=datetime.now().date(),
            help="选择统计结束日期"
        )

    with col_date3:
        refresh_data = st.button("🔄 刷新数据", use_container_width=True)

    if start_date and end_date:
        with database_session() as db:
            # 基础查询
            base_query = db.query(LocalCreative).filter(
                LocalCreative.created_at >= start_date,
                LocalCreative.created_at <= end_date
            ).options(joinedload(LocalCreative.principal))

            all_materials = base_query.all()

            if not all_materials:
                st.warning("📭 选定日期范围内没有素材数据")
                return

            # 总体统计
            st.markdown("### 📈 总体统计")
            col_stat1, col_stat2, col_stat3, col_stat4 = st.columns(4)

            total_count = len(all_materials)
            approved_count = len([m for m in all_materials if m.status == 'approved'])
            rejected_count = len([m for m in all_materials if m.status == 'rejected'])
            failed_count = len([m for m in all_materials if m.status == 'upload_failed'])

            with col_stat1:
                st.metric("总素材数", total_count)
            with col_stat2:
                approval_rate = (approved_count / total_count * 100) if total_count > 0 else 0
                st.metric("审核通过", approved_count, f"{approval_rate:.1f}%")
            with col_stat3:
                rejection_rate = (rejected_count / total_count * 100) if total_count > 0 else 0
                st.metric("审核拒绝", rejected_count, f"{rejection_rate:.1f}%")
            with col_stat4:
                failure_rate = (failed_count / total_count * 100) if total_count > 0 else 0
                st.metric("上传失败", failed_count, f"{failure_rate:.1f}%")

            # 状态分布饼图
            st.markdown("### 🥧 状态分布")
            status_counts = {}
            for material in all_materials:
                status_cn = translate_status(material.status)
                status_counts[status_cn] = status_counts.get(status_cn, 0) + 1

            if status_counts:
                status_df = pd.DataFrame(list(status_counts.items()), columns=['状态', '数量'])
                st.bar_chart(status_df.set_index('状态'))

            # 按日期统计
            st.markdown("### 📅 每日趋势")
            daily_stats = {}
            for material in all_materials:
                date_key = material.created_at.date()
                if date_key not in daily_stats:
                    daily_stats[date_key] = {
                        'total': 0, 'approved': 0, 'rejected': 0, 'failed': 0
                    }
                daily_stats[date_key]['total'] += 1
                if material.status == 'approved':
                    daily_stats[date_key]['approved'] += 1
                elif material.status == 'rejected':
                    daily_stats[date_key]['rejected'] += 1
                elif material.status == 'upload_failed':
                    daily_stats[date_key]['failed'] += 1

            if daily_stats:
                daily_df = pd.DataFrame.from_dict(daily_stats, orient='index')
                daily_df.index = pd.to_datetime(daily_df.index)
                daily_df = daily_df.sort_index()

                # 添加通过率列
                daily_df['通过率'] = (daily_df['approved'] / daily_df['total'] * 100).round(1)

                st.line_chart(daily_df[['total', 'approved', 'rejected', 'failed']])

                # 显示详细表格
                st.markdown("### 📋 每日详细数据")
                daily_display = daily_df.copy()
                daily_display.columns = ['总数', '通过', '拒绝', '失败', '通过率(%)']
                st.dataframe(daily_display, use_container_width=True)

            # 按作者统计
            st.markdown("### 👥 作者统计")
            author_stats = {}

            for material in all_materials:
                # 检查文件路径是否为空
                if not material.file_path:
                    continue

                # 从文件名提取作者信息
                filename = os.path.basename(material.file_path)
                author = "未知作者"

                # 简单的作者提取逻辑（可以根据实际文件命名规则调整）
                if "王梦珂" in filename:
                    author = "王梦珂"
                elif "杨婷婷" in filename:
                    author = "杨婷婷"
                elif "代朋飞" in filename:
                    author = "代朋飞"
                elif "谢莉" in filename:
                    author = "谢莉"

                if author not in author_stats:
                    author_stats[author] = {
                        'total': 0, 'approved': 0, 'rejected': 0, 'failed': 0
                    }

                author_stats[author]['total'] += 1
                if material.status == 'approved':
                    author_stats[author]['approved'] += 1
                elif material.status == 'rejected':
                    author_stats[author]['rejected'] += 1
                elif material.status == 'upload_failed':
                    author_stats[author]['failed'] += 1

            if author_stats:
                author_df = pd.DataFrame.from_dict(author_stats, orient='index')
                author_df['通过率'] = (author_df['approved'] / author_df['total'] * 100).round(1)
                author_df.columns = ['总数', '通过', '拒绝', '失败', '通过率(%)']

                st.dataframe(author_df, use_container_width=True)

                # 作者通过率对比
                st.markdown("### 📊 作者通过率对比")
                author_rate_df = author_df[['通过率(%)']].copy()
                st.bar_chart(author_rate_df)

def validate_directory_path(path: str) -> Tuple[bool, str]:
    """
    验证目录路径的有效性

    Args:
        path: 目录路径

    Returns:
        (是否有效, 错误信息)
    """
    if not path or not path.strip():
        return False, "路径不能为空"

    path = path.strip()

    # 检查路径格式
    if path.startswith('\\\\'):
        # 网络路径
        if len(path.split('\\')) < 4:
            return False, "网络路径格式不正确，应为 \\\\server\\share\\path"
    elif ':' in path:
        # 本地路径
        if not (len(path) >= 3 and path[1] == ':'):
            return False, "本地路径格式不正确，应为 C:\\path\\to\\directory"
    else:
        return False, "路径格式不正确，应为网络路径(\\\\server\\share)或本地路径(C:\\path)"

    # 检查路径中的非法字符
    illegal_chars = ['<', '>', '|', '*', '?', '"']
    for char in illegal_chars:
        if char in path:
            return False, f"路径包含非法字符: {char}"

    return True, ""

def batch_validate_directories(directories: List[str]) -> Dict[str, Dict]:
    """
    批量验证目录路径

    Args:
        directories: 目录路径列表

    Returns:
        验证结果字典
    """
    results = {}

    for i, directory in enumerate(directories):
        is_valid, error_msg = validate_directory_path(directory)

        results[f"目录_{i+1}"] = {
            "路径": directory,
            "状态": "✅ 有效" if is_valid else "❌ 无效",
            "错误信息": error_msg if not is_valid else "",
            "类型": "网络路径" if directory.startswith('\\\\') else "本地路径" if ':' in directory else "未知"
        }

    return results

def update_source_dirs_config(new_dirs: list) -> bool:
    """
    更新素材收集源目录配置

    Args:
        new_dirs: 新的源目录列表

    Returns:
        bool: 是否更新成功
    """
    try:
        from qianchuan_aw.utils.config_manager import get_config_manager
        import yaml

        config_manager = get_config_manager()
        config_path = config_manager._config_file_path

        # 读取当前配置
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        # 更新配置
        if 'workflow' not in config:
            config['workflow'] = {}
        if 'material_collection' not in config['workflow']:
            config['workflow']['material_collection'] = {}

        config['workflow']['material_collection']['source_dirs'] = new_dirs

        # 保存配置
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)

        # 重新加载配置
        config_manager.reload()

        backend_logger.info(f"✅ 素材收集源目录配置已更新: {len(new_dirs)} 个目录")
        return True

    except Exception as e:
        backend_logger.error(f"❌ 更新素材收集源目录配置失败: {e}")
        return False

def render_material_collection_page():
    """素材收集页面"""
    st.header("📥 素材收集")
    st.markdown("从局域网收集视频素材并入库到系统中")

    # 显示配置信息
    try:
        app_settings = load_settings()
        collection_config = app_settings.get('workflow', {}).get('material_collection', {})

        st.markdown("### ⚙️ 当前配置")
        col_config1, col_config2 = st.columns(2)

        with col_config1:
            st.markdown("**收集源目录:**")
            source_dirs = collection_config.get('source_dirs', [])
            if source_dirs:
                for i, source_dir in enumerate(source_dirs, 1):
                    st.write(f"{i}. `{source_dir}`")
            else:
                st.warning("⚠️ 未配置收集源目录")

        with col_config2:
            st.markdown("**目标目录:**")
            dest_dir = collection_config.get('dest_dir', 'G:/workflow_assets/01_materials_to_process/缇萃百货')
            st.write(f"`{dest_dir}`")

            st.markdown("**工作时间:**")
            start_hour = collection_config.get('start_hour', 8)
            start_minute = collection_config.get('start_minute', 30)
            end_hour = collection_config.get('end_hour', 20)
            end_minute = collection_config.get('end_minute', 30)
            st.write(f"{start_hour:02d}:{start_minute:02d} - {end_hour:02d}:{end_minute:02d}")

        # 检查当前是否在工作时间内
        from datetime import datetime
        current_time = datetime.now().time()
        start_time = datetime.strptime(f"{start_hour:02d}:{start_minute:02d}", "%H:%M").time()
        end_time = datetime.strptime(f"{end_hour:02d}:{end_minute:02d}", "%H:%M").time()

        is_working_hours = start_time <= current_time <= end_time

        if is_working_hours:
            st.success(f"✅ 当前时间 {current_time.strftime('%H:%M')} 在工作时间内")
        else:
            st.warning(f"⚠️ 当前时间 {current_time.strftime('%H:%M')} 不在工作时间内")

        # 增强的目录配置管理
        st.markdown("### 📁 增强目录配置管理")

        # 显示当前目录列表
        current_dirs = collection_config.get('source_dirs', [])

        # 快速统计信息
        if current_dirs:
            col_stat1, col_stat2, col_stat3, col_stat4 = st.columns(4)
            with col_stat1:
                st.metric("总目录数", len(current_dirs))
            with col_stat2:
                network_count = sum(1 for d in current_dirs if d.startswith('\\\\'))
                st.metric("网络路径", network_count)
            with col_stat3:
                local_count = len(current_dirs) - network_count
                st.metric("本地路径", local_count)
            with col_stat4:
                # 简单验证有效性
                valid_count = sum(1 for d in current_dirs if d.strip() and ('\\\\' in d or ':' in d))
                st.metric("有效目录", valid_count)

        # 选择管理模式
        management_mode = st.radio(
            "选择管理模式",
            ["📝 表格批量编辑", "📋 文本批量导入", "🔧 逐个编辑", "📊 配置分析"],
            horizontal=True,
            help="选择不同的目录管理方式"
        )

        # 根据选择的模式渲染不同的界面
        if management_mode == "📝 表格批量编辑":
            render_table_batch_editor(current_dirs)
        elif management_mode == "📋 文本批量导入":
            render_text_batch_importer(current_dirs)
        elif management_mode == "🔧 逐个编辑":
            render_individual_editor(current_dirs)
        elif management_mode == "📊 配置分析":
            render_configuration_analysis(current_dirs)

    except Exception as e:
        st.error(f"加载配置失败: {e}")

def render_table_batch_editor(current_dirs: List[str]) -> None:
    """渲染表格批量编辑界面"""
    st.markdown("#### 📝 表格批量编辑模式")
    st.info("💡 在表格中直接编辑目录路径，支持添加、删除和修改多个目录")

    # 准备表格数据
    if current_dirs:
        df_data = []
        for i, directory in enumerate(current_dirs):
            is_valid, error_msg = validate_directory_path(directory)
            df_data.append({
                "序号": i + 1,
                "目录路径": directory,
                "状态": "✅ 有效" if is_valid else "❌ 无效",
                "类型": "网络路径" if directory.startswith('\\\\') else "本地路径",
                "备注": error_msg if not is_valid else "正常"
            })

        # 显示当前配置表格
        st.markdown("##### 📋 当前目录配置")
        df = pd.DataFrame(df_data)
        st.dataframe(df, use_container_width=True, hide_index=True)
    else:
        st.warning("⚠️ 当前未配置任何源目录")

    # 批量编辑区域
    st.markdown("##### ✏️ 批量编辑区域")

    # 使用文本区域进行批量编辑
    edit_text = st.text_area(
        "编辑目录列表（每行一个目录）",
        value="\n".join(current_dirs),
        height=200,
        help="每行输入一个目录路径，支持网络路径和本地路径"
    )

    # 实时验证
    if edit_text.strip():
        new_dirs = [line.strip() for line in edit_text.split('\n') if line.strip()]

        # 显示验证结果
        if new_dirs != current_dirs:
            st.markdown("##### 🔍 变更预览")
            validation_results = batch_validate_directories(new_dirs)

            # 统计信息
            total_dirs = len(new_dirs)
            valid_dirs = sum(1 for result in validation_results.values() if "✅" in result["状态"])
            invalid_dirs = total_dirs - valid_dirs

            col_stats1, col_stats2, col_stats3 = st.columns(3)
            with col_stats1:
                st.metric("总目录数", total_dirs, total_dirs - len(current_dirs))
            with col_stats2:
                st.metric("有效目录", valid_dirs)
            with col_stats3:
                st.metric("无效目录", invalid_dirs)

            # 显示验证结果表格
            if validation_results:
                validation_df = pd.DataFrame.from_dict(validation_results, orient='index')
                st.dataframe(validation_df, use_container_width=True)

            # 保存按钮
            col_save, col_cancel = st.columns(2)
            with col_save:
                if st.button("💾 保存更改", use_container_width=True, type="primary"):
                    if invalid_dirs == 0:
                        if update_source_dirs_config(new_dirs):
                            st.success(f"✅ 成功更新 {total_dirs} 个目录配置")
                            st.rerun()
                        else:
                            st.error("❌ 保存失败")
                    else:
                        st.error(f"❌ 存在 {invalid_dirs} 个无效目录，请修正后再保存")

            with col_cancel:
                if st.button("🔄 重置", use_container_width=True):
                    st.rerun()

def render_text_batch_importer(current_dirs: List[str]) -> None:
    """渲染文本批量导入界面"""
    st.markdown("#### 📋 文本批量导入模式")
    st.info("💡 支持从文本文件导入、Excel导入或直接粘贴目录列表")

    import_method = st.selectbox(
        "选择导入方式",
        ["📝 直接粘贴", "📄 文本文件上传", "📊 Excel文件上传"]
    )

    new_dirs = []

    if import_method == "📝 直接粘贴":
        paste_text = st.text_area(
            "粘贴目录列表",
            height=150,
            placeholder="每行一个目录路径，例如：\n\\\\server\\share\\path1\n\\\\server\\share\\path2\nD:\\local\\path"
        )

        if paste_text.strip():
            new_dirs = [line.strip() for line in paste_text.split('\n') if line.strip()]

    elif import_method == "📄 文本文件上传":
        uploaded_file = st.file_uploader(
            "上传文本文件",
            type=['txt'],
            help="文本文件中每行一个目录路径"
        )

        if uploaded_file is not None:
            try:
                content = uploaded_file.read().decode('utf-8')
                new_dirs = [line.strip() for line in content.split('\n') if line.strip()]
                st.success(f"✅ 成功读取 {len(new_dirs)} 个目录路径")
            except Exception as e:
                st.error(f"❌ 文件读取失败: {e}")

    elif import_method == "📊 Excel文件上传":
        uploaded_file = st.file_uploader(
            "上传Excel文件",
            type=['xlsx', 'xls'],
            help="Excel文件第一列应包含目录路径"
        )

        if uploaded_file is not None:
            try:
                df = pd.read_excel(uploaded_file)
                if not df.empty:
                    # 取第一列作为目录路径
                    new_dirs = [str(path).strip() for path in df.iloc[:, 0].tolist() if str(path).strip() != 'nan']
                    st.success(f"✅ 成功读取 {len(new_dirs)} 个目录路径")

                    # 显示预览
                    st.markdown("##### 📋 导入预览")
                    preview_df = pd.DataFrame({"目录路径": new_dirs[:10]})  # 只显示前10个
                    st.dataframe(preview_df, use_container_width=True)
                    if len(new_dirs) > 10:
                        st.info(f"显示前10个目录，总共 {len(new_dirs)} 个")
            except Exception as e:
                st.error(f"❌ Excel文件读取失败: {e}")

    # 处理导入的目录
    if new_dirs:
        st.markdown("##### 🔍 导入验证")

        # 验证导入的目录
        validation_results = batch_validate_directories(new_dirs)

        # 统计信息
        total_dirs = len(new_dirs)
        valid_dirs = sum(1 for result in validation_results.values() if "✅" in result["状态"])
        invalid_dirs = total_dirs - valid_dirs
        duplicate_dirs = len(new_dirs) - len(set(new_dirs))

        col_stats1, col_stats2, col_stats3, col_stats4 = st.columns(4)
        with col_stats1:
            st.metric("导入目录数", total_dirs)
        with col_stats2:
            st.metric("有效目录", valid_dirs)
        with col_stats3:
            st.metric("无效目录", invalid_dirs)
        with col_stats4:
            st.metric("重复目录", duplicate_dirs)

        # 显示验证结果
        if validation_results:
            validation_df = pd.DataFrame.from_dict(validation_results, orient='index')
            st.dataframe(validation_df, use_container_width=True)

        # 导入选项
        st.markdown("##### ⚙️ 导入选项")
        col_option1, col_option2 = st.columns(2)

        with col_option1:
            import_mode = st.radio(
                "导入模式",
                ["🔄 替换现有配置", "➕ 追加到现有配置"],
                help="替换：清空现有配置并导入新配置\n追加：在现有配置基础上添加新目录"
            )

        with col_option2:
            auto_dedupe = st.checkbox("🔧 自动去重", value=True, help="自动移除重复的目录路径")
            skip_invalid = st.checkbox("⚠️ 跳过无效目录", value=True, help="只导入有效的目录路径")

        # 执行导入
        col_import, col_cancel = st.columns(2)
        with col_import:
            if st.button("📥 执行导入", use_container_width=True, type="primary"):
                try:
                    # 处理导入的目录
                    processed_dirs = new_dirs.copy()

                    # 去重
                    if auto_dedupe:
                        processed_dirs = list(dict.fromkeys(processed_dirs))  # 保持顺序的去重

                    # 跳过无效目录
                    if skip_invalid:
                        valid_processed_dirs = []
                        for directory in processed_dirs:
                            is_valid, _ = validate_directory_path(directory)
                            if is_valid:
                                valid_processed_dirs.append(directory)
                        processed_dirs = valid_processed_dirs

                    # 根据导入模式处理
                    if import_mode == "🔄 替换现有配置":
                        final_dirs = processed_dirs
                    else:  # 追加模式
                        final_dirs = current_dirs + processed_dirs
                        if auto_dedupe:
                            final_dirs = list(dict.fromkeys(final_dirs))

                    # 执行更新
                    if update_source_dirs_config(final_dirs):
                        st.success(f"✅ 成功导入 {len(processed_dirs)} 个目录，当前总计 {len(final_dirs)} 个目录")
                        st.rerun()
                    else:
                        st.error("❌ 导入失败")

                except Exception as e:
                    st.error(f"❌ 导入过程中发生错误: {e}")

        with col_cancel:
            if st.button("❌ 取消导入", use_container_width=True):
                st.rerun()

def render_individual_editor(current_dirs: List[str]) -> None:
    """渲染逐个编辑界面（改进版）"""
    st.markdown("#### 🔧 逐个编辑模式")
    st.info("💡 逐个编辑每个目录，支持拖拽排序和快速操作")

    if not current_dirs:
        st.warning("⚠️ 当前未配置任何源目录")

        # 添加新目录功能
        st.markdown("##### ➕ 添加新目录")
        with st.form("add_new_dir_form"):
            new_dir = st.text_input(
                "新目录路径",
                placeholder="例如: \\\\SERVER\\path\\to\\videos 或 D:\\local\\videos",
                help="支持网络路径和本地路径"
            )

            col_add, col_example = st.columns([1, 2])
            with col_add:
                submitted = st.form_submit_button("➕ 添加目录", use_container_width=True)
            with col_example:
                st.markdown("**示例路径:**")
                st.code("\\\\OS-20231014VKFP\\谢莉\\七月视频\\7.17", language=None)

            if submitted:
                if not new_dir.strip():
                    st.error("❌ 目录路径不能为空")
                else:
                    is_valid, error_msg = validate_directory_path(new_dir.strip())
                    if not is_valid:
                        st.error(f"❌ 目录路径无效: {error_msg}")
                    else:
                        if update_source_dirs_config([new_dir.strip()]):
                            st.success(f"✅ 已添加新目录: {new_dir.strip()}")
                            st.rerun()
                        else:
                            st.error("❌ 添加失败")
        return

    # 使用会话状态管理编辑状态
    if 'enhanced_edit_mode' not in st.session_state:
        st.session_state.enhanced_edit_mode = {}

    # 显示目录列表
    st.markdown("##### 📋 目录列表")

    for i, directory in enumerate(current_dirs):
        with st.container():
            col_index, col_path, col_status, col_actions = st.columns([0.5, 4, 1, 2])

            with col_index:
                st.markdown(f"**{i+1}**")

            with col_path:
                if st.session_state.enhanced_edit_mode.get(i, False):
                    # 编辑模式
                    edited_dir = st.text_input(
                        f"编辑目录 {i+1}",
                        value=directory,
                        key=f"enhanced_edit_input_{i}",
                        label_visibility="collapsed"
                    )
                else:
                    # 显示模式
                    st.code(directory, language=None)

            with col_status:
                is_valid, _ = validate_directory_path(directory)
                st.markdown("✅" if is_valid else "❌")

            with col_actions:
                if st.session_state.enhanced_edit_mode.get(i, False):
                    # 编辑模式的操作按钮
                    col_save, col_cancel = st.columns(2)
                    with col_save:
                        if st.button("💾", key=f"enhanced_save_{i}", help="保存"):
                            edited_dir = st.session_state.get(f"enhanced_edit_input_{i}", directory)
                            if edited_dir.strip() != directory:
                                is_valid, error_msg = validate_directory_path(edited_dir.strip())
                                if not is_valid:
                                    st.error(f"❌ 目录路径无效: {error_msg}")
                                else:
                                    new_dirs = current_dirs.copy()
                                    new_dirs[i] = edited_dir.strip()
                                    if update_source_dirs_config(new_dirs):
                                        st.success(f"✅ 已更新目录 {i+1}")
                                        st.session_state.enhanced_edit_mode[i] = False
                                        st.rerun()
                                    else:
                                        st.error("❌ 更新失败")
                            else:
                                st.session_state.enhanced_edit_mode[i] = False
                                st.rerun()
                    with col_cancel:
                        if st.button("❌", key=f"enhanced_cancel_{i}", help="取消"):
                            st.session_state.enhanced_edit_mode[i] = False
                            st.rerun()
                else:
                    # 显示模式的操作按钮
                    col_edit, col_delete, col_move = st.columns(3)
                    with col_edit:
                        if st.button("✏️", key=f"enhanced_edit_{i}", help="编辑"):
                            st.session_state.enhanced_edit_mode[i] = True
                            st.rerun()
                    with col_delete:
                        if st.button("🗑️", key=f"enhanced_delete_{i}", help="删除"):
                            new_dirs = [d for j, d in enumerate(current_dirs) if j != i]
                            if update_source_dirs_config(new_dirs):
                                st.success(f"✅ 已删除目录 {i+1}")
                                # 清理编辑状态
                                if i in st.session_state.enhanced_edit_mode:
                                    del st.session_state.enhanced_edit_mode[i]
                                st.rerun()
                            else:
                                st.error("❌ 删除失败")
                    with col_move:
                        # 移动按钮（上移/下移）
                        if i > 0:
                            if st.button("⬆️", key=f"enhanced_up_{i}", help="上移"):
                                new_dirs = current_dirs.copy()
                                new_dirs[i], new_dirs[i-1] = new_dirs[i-1], new_dirs[i]
                                if update_source_dirs_config(new_dirs):
                                    st.rerun()
                        if i < len(current_dirs) - 1:
                            if st.button("⬇️", key=f"enhanced_down_{i}", help="下移"):
                                new_dirs = current_dirs.copy()
                                new_dirs[i], new_dirs[i+1] = new_dirs[i+1], new_dirs[i]
                                if update_source_dirs_config(new_dirs):
                                    st.rerun()

            st.divider()

    # 添加新目录功能
    st.markdown("##### ➕ 添加新目录")
    with st.form("add_individual_dir_form"):
        new_dir = st.text_input(
            "新目录路径",
            placeholder="例如: \\\\SERVER\\path\\to\\videos 或 D:\\local\\videos",
            help="支持网络路径和本地路径"
        )

        col_add, col_example = st.columns([1, 2])
        with col_add:
            submitted = st.form_submit_button("➕ 添加目录", use_container_width=True)
        with col_example:
            st.markdown("**示例路径:**")
            st.code("\\\\OS-20231014VKFP\\谢莉\\七月视频\\7.17", language=None)

        if submitted:
            if not new_dir.strip():
                st.error("❌ 目录路径不能为空")
            elif new_dir.strip() in current_dirs:
                st.warning("⚠️ 该目录已存在")
            else:
                is_valid, error_msg = validate_directory_path(new_dir.strip())
                if not is_valid:
                    st.error(f"❌ 目录路径无效: {error_msg}")
                else:
                    new_dirs = current_dirs + [new_dir.strip()]
                    if update_source_dirs_config(new_dirs):
                        st.success(f"✅ 已添加新目录: {new_dir.strip()}")
                        st.rerun()
                    else:
                        st.error("❌ 添加失败")

    # 批量操作
    st.markdown("##### 🔄 批量操作")
    col_clear, col_reset = st.columns(2)

    with col_clear:
        if st.button("🗑️ 清空所有目录", use_container_width=True):
            if st.session_state.get('confirm_clear_dirs', False):
                if update_source_dirs_config([]):
                    st.success("✅ 已清空所有源目录")
                    st.session_state.confirm_clear_dirs = False
                    st.rerun()
                else:
                    st.error("❌ 清空失败")
            else:
                st.session_state.confirm_clear_dirs = True
                st.warning("⚠️ 再次点击确认清空所有目录")

    with col_reset:
        if st.button("🔄 重置为默认", use_container_width=True):
            default_dirs = [
                "\\\\OS-20231014VKFP\\谢莉\\七月视频\\7.17",
                "\\\\OS-20231107OWPW\\代朋飞成片\\2025\\7.17-代朋飞-",
                "\\\\OS-20231126UOSQ\\婷婷成品\\杨婷婷-7.17"
            ]
            if update_source_dirs_config(default_dirs):
                st.success("✅ 已重置为默认目录配置")
                st.rerun()
            else:
                st.error("❌ 重置失败")

def render_configuration_analysis(current_dirs: List[str]) -> None:
    """渲染配置分析界面"""
    st.markdown("#### 📊 配置分析模式")
    st.info("💡 分析当前目录配置的统计信息和潜在问题")

    if not current_dirs:
        st.warning("⚠️ 当前未配置任何源目录")
        return

    # 基本统计
    st.markdown("##### 📈 基本统计")
    col_stats1, col_stats2, col_stats3, col_stats4 = st.columns(4)

    total_dirs = len(current_dirs)
    network_dirs = sum(1 for d in current_dirs if d.startswith('\\\\'))
    local_dirs = total_dirs - network_dirs

    validation_results = batch_validate_directories(current_dirs)
    valid_dirs = sum(1 for result in validation_results.values() if "✅" in result["状态"])
    invalid_dirs = total_dirs - valid_dirs

    with col_stats1:
        st.metric("总目录数", total_dirs)
    with col_stats2:
        st.metric("网络路径", network_dirs)
    with col_stats3:
        st.metric("本地路径", local_dirs)
    with col_stats4:
        st.metric("有效目录", valid_dirs)

    # 详细分析表格
    st.markdown("##### 📋 详细分析")
    if validation_results:
        analysis_df = pd.DataFrame.from_dict(validation_results, orient='index')
        st.dataframe(analysis_df, use_container_width=True)

    # 问题检测
    st.markdown("##### ⚠️ 问题检测")
    issues = []

    if invalid_dirs > 0:
        issues.append(f"发现 {invalid_dirs} 个无效目录路径")

    # 检查重复目录
    duplicates = len(current_dirs) - len(set(current_dirs))
    if duplicates > 0:
        issues.append(f"发现 {duplicates} 个重复目录")

    # 检查空目录
    empty_dirs = sum(1 for d in current_dirs if not d.strip())
    if empty_dirs > 0:
        issues.append(f"发现 {empty_dirs} 个空目录")

    if issues:
        for issue in issues:
            st.error(f"❌ {issue}")
    else:
        st.success("✅ 未发现配置问题")

    # 建议
    st.markdown("##### 💡 优化建议")
    suggestions = []

    if network_dirs == 0:
        suggestions.append("考虑添加网络路径以支持多机器素材收集")

    if local_dirs == 0:
        suggestions.append("考虑添加本地路径作为备用素材源")

    if total_dirs < 3:
        suggestions.append("建议配置更多素材源目录以提高收集效率")

    if total_dirs > 20:
        suggestions.append("目录数量较多，建议定期清理不活跃的目录")

    if suggestions:
        for suggestion in suggestions:
            st.info(f"💡 {suggestion}")
    else:
        st.success("✅ 当前配置较为合理")

    # 导出功能
    st.markdown("##### 📤 导出配置")
    col_export1, col_export2, col_export3 = st.columns(3)

    with col_export1:
        if st.button("📄 导出为文本", use_container_width=True):
            export_content = "\n".join(current_dirs)
            st.download_button(
                label="💾 下载文本文件",
                data=export_content,
                file_name=f"source_dirs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                mime="text/plain"
            )

    with col_export2:
        if st.button("📊 导出为Excel", use_container_width=True):
            df_export = pd.DataFrame({
                "序号": range(1, len(current_dirs) + 1),
                "目录路径": current_dirs,
                "类型": ["网络路径" if d.startswith('\\\\') else "本地路径" for d in current_dirs]
            })

            # 转换为Excel格式
            import io
            output = io.BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df_export.to_excel(writer, index=False, sheet_name='源目录配置')

            st.download_button(
                label="💾 下载Excel文件",
                data=output.getvalue(),
                file_name=f"source_dirs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            )

    with col_export3:
        if st.button("🔧 导出为JSON", use_container_width=True):
            import json
            export_data = {
                "export_time": datetime.now().isoformat(),
                "total_directories": len(current_dirs),
                "source_directories": current_dirs
            }
            export_content = json.dumps(export_data, indent=2, ensure_ascii=False)

            st.download_button(
                label="💾 下载JSON文件",
                data=export_content,
                file_name=f"source_dirs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                mime="application/json"
            )

        # 操作按钮区域
        st.markdown("### 🎛️ 操作面板")
        col_op1, col_op2, col_op3 = st.columns(3)

        with col_op1:
            if st.button("📥 执行素材收集", use_container_width=True):
                with st.spinner("正在执行素材收集..."):
                    try:
                        # 导入并执行素材收集任务
                        from qianchuan_aw.workflows.scheduler import handle_material_collection
                        from qianchuan_aw.utils.db_utils import database_session

                        # 重新加载配置
                        app_settings = load_settings()

                        with database_session() as db:
                            handle_material_collection(db, app_settings)

                        st.success("✅ 素材收集任务执行完成")
                        st.rerun()  # 刷新页面显示最新状态

                    except Exception as e:
                        st.error(f"❌ 素材收集失败: {e}")

        with col_op2:
            if st.button("📂 执行文件摄取", use_container_width=True):
                with st.spinner("正在执行文件摄取..."):
                    try:
                        # 导入并执行文件摄取任务
                        from qianchuan_aw.workflows.scheduler import handle_file_ingestion
                        from qianchuan_aw.utils.db_utils import database_session

                        # 重新加载配置
                        app_settings = load_settings()

                        with database_session() as db:
                            handle_file_ingestion(db, app_settings)

                        st.success("✅ 文件摄取任务执行完成")
                        st.rerun()  # 刷新页面显示最新状态

                    except Exception as e:
                        st.error(f"❌ 文件摄取失败: {e}")

        with col_op3:
            if st.button("🔄 收集+摄取", use_container_width=True):
                with st.spinner("正在执行素材收集和文件摄取..."):
                    try:
                        from qianchuan_aw.workflows.scheduler import handle_material_collection, handle_file_ingestion
                        from qianchuan_aw.utils.db_utils import database_session

                        # 重新加载配置
                        app_settings = load_settings()

                        with database_session() as db:
                            # 先收集素材
                            handle_material_collection(db, app_settings)
                            # 再摄取文件
                            handle_file_ingestion(db, app_settings)

                        st.success("✅ 素材收集和文件摄取任务执行完成")
                        st.rerun()  # 刷新页面显示最新状态

                    except Exception as e:
                        st.error(f"❌ 任务执行失败: {e}")

        # 显示最近的素材统计
        st.markdown("### 📊 最近素材统计")
        try:
            with database_session() as db:
                from datetime import datetime, timedelta

                # 今天新增的素材
                today = datetime.now().date()
                today_materials = db.query(LocalCreative).filter(
                    LocalCreative.created_at >= today
                ).all()

                # 最近7天的素材
                week_ago = today - timedelta(days=7)
                week_materials = db.query(LocalCreative).filter(
                    LocalCreative.created_at >= week_ago
                ).all()

                col_stat1, col_stat2, col_stat3 = st.columns(3)

                with col_stat1:
                    st.metric("今日新增", len(today_materials))

                with col_stat2:
                    st.metric("本周新增", len(week_materials))

                with col_stat3:
                    # 待处理素材数量
                    pending_count = db.query(LocalCreative).filter(
                        LocalCreative.status == 'pending_grouping'
                    ).count()
                    st.metric("待处理", pending_count)

        except Exception as e:
            st.error(f"获取统计信息失败: {e}")

def render_tested_materials_page():
    """已测试素材管理页面"""
    st.header("✅ 已测试素材管理")
    st.markdown("管理已经创建过测试计划的素材，避免重复测试浪费资源")

    # 导入工具
    try:
        from tools import manage_already_tested as mat_tool
    except ImportError:
        st.error("❌ 无法导入已测试素材管理工具")
        return

    # 操作按钮区域
    st.markdown("### 🎛️ 操作面板")
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        if st.button("📋 查看已测试素材", use_container_width=True):
            with st.spinner("正在查询已测试素材..."):
                try:
                    materials = mat_tool.list_already_tested_materials()

                    if materials:
                        st.success(f"✅ 找到 {len(materials)} 个已测试素材")

                        # 显示统计信息
                        total_campaigns = sum(m['campaign_count'] for m in materials)

                        # 统计指标
                        col_a, col_b, col_c = st.columns(3)
                        with col_a:
                            st.metric("已测试素材数", len(materials))
                        with col_b:
                            st.metric("总测试计划数", total_campaigns)
                        with col_c:
                            avg_campaigns = total_campaigns / len(materials) if materials else 0
                            st.metric("平均计划数/素材", f"{avg_campaigns:.1f}")

                        # 按主体分组显示
                        st.markdown("### 📊 按主体分组统计")
                        principal_stats = {}
                        for material in materials:
                            principal = material['principal_name']
                            if principal not in principal_stats:
                                principal_stats[principal] = {'count': 0, 'campaigns': 0}
                            principal_stats[principal]['count'] += 1
                            principal_stats[principal]['campaigns'] += material['campaign_count']

                        for principal, stats in principal_stats.items():
                            st.write(f"**{principal}**: {stats['count']}个素材, {stats['campaigns']}个测试计划")

                        # 详细列表
                        st.markdown("### 📋 详细素材列表")
                        for material in materials[:20]:  # 只显示前20个
                            with st.expander(f"📹 {material['file_name']} ({material['principal_name']})"):
                                col_x, col_y = st.columns(2)
                                with col_x:
                                    st.write(f"**测试计划数**: {material['campaign_count']}")
                                    st.write(f"**当前状态**: {material['status']}")
                                with col_y:
                                    st.write(f"**创建时间**: {material['created_at']}")
                                    st.write(f"**更新时间**: {material['updated_at']}")

                        if len(materials) > 20:
                            st.info(f"仅显示前20个素材，总共有{len(materials)}个")
                    else:
                        st.info("📭 没有找到已测试的素材")

                except Exception as e:
                    st.error(f"❌ 查询失败: {e}")

    with col2:
        if st.button("📁 移动已测试文件", use_container_width=True):
            with st.spinner("正在移动已测试文件..."):
                try:
                    run_with_log_capture(mat_tool.move_already_tested_files)
                    st.success("✅ 已测试文件移动完成")
                except Exception as e:
                    st.error(f"❌ 移动失败: {e}")

    with col3:
        if st.button("📊 生成详细报告", use_container_width=True):
            with st.spinner("正在生成报告..."):
                try:
                    report_file = mat_tool.generate_already_tested_report()
                    st.success(f"✅ 报告生成完成: {report_file}")
                except Exception as e:
                    st.error(f"❌ 报告生成失败: {e}")

    with col4:
        if st.button("🔄 清理状态", use_container_width=True):
            with st.spinner("正在清理已测试状态..."):
                try:
                    count = mat_tool.cleanup_already_tested_status()
                    st.success(f"✅ 已更新 {count} 个素材状态")
                except Exception as e:
                    st.error(f"❌ 清理失败: {e}")

def main():
    st.sidebar.title("🎯 千川自动化管理系统")

    # 渲染全局账户选择器
    render_global_account_selector()

    # 渲染全局收藏管理界面（如果需要）
    render_global_favorite_manager()

    st.sidebar.markdown("---")

    # 使用selectbox替代radio，更清晰的分类
    page_category = st.sidebar.selectbox(
        "选择功能分类",
        ["📊 核心业务", "🎬 素材管理", "🛠️ 运营工具", "⚙️ 系统功能"]
    )

    # 根据分类显示对应页面
    if page_category == "📊 核心业务":
        pages = {
            "🚀 投放中心": render_launch_center_page,
            "🏢 账户管理": render_accounts_page,
        }
    elif page_category == "🎬 素材管理":
        pages = {
            "🔍 视频素材搜索": show_material_search_page,
            "❌ 失败素材管理": show_failed_materials_manager,
            "✅ 已测试素材管理": render_tested_materials_page,
            "📊 素材审核报表 (完整版)": render_complete_material_analytics_page,
            "📊 素材审核报表 (增强版)": render_enhanced_material_analytics_page,
            "📊 素材审核报表 (旧版)": render_material_analytics_page,
            "📥 素材收集": render_material_collection_page,
        }
    elif page_category == "🛠️ 运营工具":
        pages = {
            "🎯 批量投放工具": render_operation_tools_page,
            "🚀 智能提审调度器": render_smart_appeal_scheduler_page,
            "🔧 系统管理工具": render_admin_tools_page,
        }
    else:  # 系统功能
        pages = {
            "🔍 系统监控": render_system_monitoring_page,
            "🐛 系统调试": render_debug_page,
            "📸 快照与备份": render_snapshot_page,
            "⚙️ 系统设置": render_settings_page,
        }

    # 选择具体页面
    selection = st.sidebar.radio("选择功能页面", list(pages.keys()))

    if selection and selection in pages:
        page_function = pages[selection]
        page_function()
    else:
        st.error("请选择一个有效的功能页面")

if __name__ == "__main__":
    main()
