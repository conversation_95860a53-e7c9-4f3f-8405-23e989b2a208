#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
收割和重新投放工具
"""
import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# 设置路径
project_root = Path(__file__).parent.parent
src_path = project_root / "src"
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(src_path))

from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import Campaign, AdAccount, Principal, LocalCreative
from qianchuan_aw.utils.logger import logger
from qianchuan_aw.sdk_qc.client import QianchuanClient
from qianchuan_aw.utils.config_manager import get_config_manager

def get_performance_campaigns(days: int = 7, min_cost: float = 100.0):
    """
    获取表现良好的计划
    
    Args:
        days: 查看过去几天的数据
        min_cost: 最小消费金额
        
    Returns:
        表现良好的计划列表
    """
    try:
        config_manager = get_config_manager()
        app_settings = config_manager.get_config()
        
        with database_session() as db:
            # 获取所有活跃计划
            campaigns = db.query(Campaign).join(AdAccount).filter(
                Campaign.status.in_(['ENABLE', 'AUDIT_APPROVED']),
                AdAccount.status == 'active'
            ).all()
            
            good_campaigns = []
            
            for campaign in campaigns:
                try:
                    client = QianchuanClient(
                        app_id=app_settings['api_credentials']['app_id'],
                        secret=app_settings['api_credentials']['secret'],
                        principal_id=campaign.account.principal.id
                    )
                    
                    # 获取计划数据报告
                    end_date = datetime.now()
                    start_date = end_date - timedelta(days=days)
                    
                    report = client.get_campaign_report(
                        advertiser_id=campaign.account.account_id_qc,
                        campaign_id=campaign.campaign_id_qc,
                        start_date=start_date.strftime('%Y-%m-%d'),
                        end_date=end_date.strftime('%Y-%m-%d')
                    )
                    
                    if report and len(report) > 0:
                        total_cost = sum(float(r.get('cost', 0)) for r in report)
                        total_conversions = sum(int(r.get('convert_cnt', 0)) for r in report)
                        
                        # 判断表现是否良好
                        if total_cost >= min_cost and total_conversions > 0:
                            cpa = total_cost / total_conversions if total_conversions > 0 else float('inf')
                            
                            good_campaigns.append({
                                'campaign': campaign,
                                'cost': total_cost,
                                'conversions': total_conversions,
                                'cpa': cpa
                            })
                            
                except Exception as e:
                    logger.warning(f"获取计划 {campaign.campaign_id_qc} 数据失败: {e}")
                    continue
            
            # 按CPA排序
            good_campaigns.sort(key=lambda x: x['cpa'])
            return good_campaigns
            
    except Exception as e:
        logger.error(f"获取表现良好的计划失败: {e}")
        return []

def harvest_campaign(campaign_id: str) -> bool:
    """
    收割计划（暂停并记录）
    
    Args:
        campaign_id: 计划ID
        
    Returns:
        是否收割成功
    """
    try:
        config_manager = get_config_manager()
        app_settings = config_manager.get_config()
        
        with database_session() as db:
            campaign = db.query(Campaign).filter(
                Campaign.campaign_id_qc == campaign_id
            ).first()
            
            if not campaign:
                logger.error(f"未找到计划: {campaign_id}")
                return False
            
            client = QianchuanClient(
                app_id=app_settings['api_credentials']['app_id'],
                secret=app_settings['api_credentials']['secret'],
                principal_id=campaign.account.principal.id
            )
            
            # 暂停计划
            result = client.update_campaign_status(
                advertiser_id=campaign.account.account_id_qc,
                campaign_id=campaign_id,
                status='DISABLE'
            )
            
            if result:
                # 更新数据库状态
                campaign.status = 'DISABLE'
                campaign.updated_at = datetime.now()
                db.commit()
                
                logger.success(f"✅ 计划 {campaign_id} 收割成功")
                return True
            else:
                logger.error(f"暂停计划 {campaign_id} 失败")
                return False
                
    except Exception as e:
        logger.error(f"收割计划失败: {e}")
        return False

def relaunch_with_new_creative(campaign_id: str, new_creative_path: str = None) -> bool:
    """
    使用新素材重新投放
    
    Args:
        campaign_id: 原计划ID
        new_creative_path: 新素材路径（可选）
        
    Returns:
        是否重新投放成功
    """
    try:
        config_manager = get_config_manager()
        app_settings = config_manager.get_config()
        
        with database_session() as db:
            # 获取原计划
            original_campaign = db.query(Campaign).filter(
                Campaign.campaign_id_qc == campaign_id
            ).first()
            
            if not original_campaign:
                logger.error(f"未找到原计划: {campaign_id}")
                return False
            
            client = QianchuanClient(
                app_id=app_settings['api_credentials']['app_id'],
                secret=app_settings['api_credentials']['secret'],
                principal_id=original_campaign.account.principal.id
            )
            
            # 获取原计划配置
            campaign_config = client.get_campaign_config(campaign_id)
            if not campaign_config:
                logger.error("获取原计划配置失败")
                return False
            
            # 修改计划名称
            campaign_config['name'] = f"{original_campaign.name}_重投_{datetime.now().strftime('%m%d')}"
            
            # 如果提供了新素材，替换素材
            if new_creative_path and os.path.exists(new_creative_path):
                # 上传新素材
                upload_result = client.upload_video(
                    advertiser_id=original_campaign.account.account_id_qc,
                    video_path=new_creative_path
                )
                
                if upload_result and upload_result.get('video_id'):
                    campaign_config['creative']['video_id'] = upload_result['video_id']
                    logger.info(f"使用新素材: {upload_result['video_id']}")
            
            # 创建新计划
            result = client.create_campaign(campaign_config)
            
            if result and result.get('campaign_id'):
                new_campaign_id = result['campaign_id']
                
                # 保存到数据库
                new_campaign = Campaign(
                    campaign_id_qc=str(new_campaign_id),
                    name=campaign_config['name'],
                    account_id=original_campaign.account_id,
                    status='CREATED'
                )
                
                db.add(new_campaign)
                db.commit()
                
                logger.success(f"✅ 重新投放成功: {new_campaign_id}")
                return True
            else:
                logger.error("创建新计划失败")
                return False
                
    except Exception as e:
        logger.error(f"重新投放失败: {e}")
        return False

def run_harvest_and_relaunch(advertiser_id: int, principal_id: int, start_date: str, end_date: str,
                             min_cost: float = 100.0, min_conversions: int = 1,
                             auto_relaunch: bool = False, new_creative_path: str = None):
    """
    运行收割和重新投放流程 - Web UI调用的主函数

    Args:
        advertiser_id: 广告主ID
        principal_id: 主体ID
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)
        min_cost: 最小消费金额
        min_conversions: 最小转化数
        auto_relaunch: 是否自动重新投放
        new_creative_path: 新素材路径

    Returns:
        dict: 执行结果
    """
    try:
        logger.info(f"🔄 开始收割和重新投放流程")
        logger.info(f"📋 参数: advertiser_id={advertiser_id}, principal_id={principal_id}")
        logger.info(f"📅 时间范围: {start_date} 到 {end_date}")
        logger.info(f"💰 最小消费: {min_cost}, 最小转化: {min_conversions}")

        config_manager = get_config_manager()
        app_settings = config_manager.get_config()

        # 计算天数
        from datetime import datetime
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        days = (end_dt - start_dt).days + 1

        results = {
            'success': False,
            'message': '',
            'good_campaigns': [],
            'harvested_campaigns': [],
            'relaunched_campaigns': [],
            'errors': []
        }

        with database_session() as db:
            # 获取指定账户的计划
            campaigns = db.query(Campaign).join(AdAccount).filter(
                Campaign.status.in_(['ENABLE', 'AUDIT_APPROVED']),
                AdAccount.account_id_qc == str(advertiser_id),
                AdAccount.status == 'active'
            ).all()

            if not campaigns:
                results['message'] = f"未找到账户 {advertiser_id} 的活跃计划"
                logger.warning(results['message'])
                return results

            logger.info(f"📊 找到 {len(campaigns)} 个活跃计划，开始分析表现...")

            client = QianchuanClient(
                app_id=app_settings['api_credentials']['app_id'],
                secret=app_settings['api_credentials']['secret'],
                principal_id=principal_id
            )

            good_campaigns = []

            for campaign in campaigns:
                try:
                    # 获取计划数据报告
                    report = client.get_campaign_report(
                        advertiser_id=advertiser_id,
                        campaign_id=campaign.campaign_id_qc,
                        start_date=start_date,
                        end_date=end_date
                    )

                    if report and len(report) > 0:
                        total_cost = sum(float(r.get('cost', 0)) for r in report)
                        total_conversions = sum(int(r.get('convert_cnt', 0)) for r in report)

                        # 判断表现是否良好
                        if total_cost >= min_cost and total_conversions >= min_conversions:
                            cpa = total_cost / total_conversions if total_conversions > 0 else float('inf')

                            campaign_info = {
                                'campaign': campaign,
                                'campaign_id': campaign.campaign_id_qc,
                                'name': campaign.name,
                                'cost': total_cost,
                                'conversions': total_conversions,
                                'cpa': cpa
                            }

                            good_campaigns.append(campaign_info)
                            logger.success(f"✅ 发现优质计划: {campaign.name} (消费: {total_cost:.2f}, 转化: {total_conversions}, CPA: {cpa:.2f})")

                except Exception as e:
                    error_msg = f"分析计划 {campaign.campaign_id_qc} 失败: {e}"
                    logger.warning(error_msg)
                    results['errors'].append(error_msg)
                    continue

            # 按CPA排序
            good_campaigns.sort(key=lambda x: x['cpa'])
            results['good_campaigns'] = good_campaigns

            if not good_campaigns:
                results['message'] = f"在指定时间范围内未找到表现良好的计划 (最小消费: {min_cost}, 最小转化: {min_conversions})"
                logger.info(results['message'])
                results['success'] = True  # 没有找到也算成功执行
                return results

            logger.info(f"🎯 发现 {len(good_campaigns)} 个表现良好的计划")

            # 如果启用自动重新投放
            if auto_relaunch:
                logger.info("🚀 开始自动收割和重新投放流程...")

                for campaign_info in good_campaigns[:5]:  # 限制处理前5个最优计划
                    campaign_id = campaign_info['campaign_id']

                    try:
                        # 收割计划
                        if harvest_campaign(campaign_id):
                            results['harvested_campaigns'].append(campaign_info)
                            logger.success(f"✅ 收割成功: {campaign_info['name']}")

                            # 重新投放
                            if relaunch_with_new_creative(campaign_id, new_creative_path):
                                results['relaunched_campaigns'].append(campaign_info)
                                logger.success(f"🚀 重新投放成功: {campaign_info['name']}")
                            else:
                                error_msg = f"重新投放失败: {campaign_info['name']}"
                                logger.error(error_msg)
                                results['errors'].append(error_msg)
                        else:
                            error_msg = f"收割失败: {campaign_info['name']}"
                            logger.error(error_msg)
                            results['errors'].append(error_msg)

                    except Exception as e:
                        error_msg = f"处理计划 {campaign_info['name']} 失败: {e}"
                        logger.error(error_msg)
                        results['errors'].append(error_msg)

            # 生成结果消息
            if auto_relaunch:
                results['message'] = f"收割和重新投放完成: 发现 {len(good_campaigns)} 个优质计划, 收割 {len(results['harvested_campaigns'])} 个, 重新投放 {len(results['relaunched_campaigns'])} 个"
            else:
                results['message'] = f"优质素材分析完成: 发现 {len(good_campaigns)} 个表现良好的计划"

            results['success'] = True
            logger.success(f"🎉 {results['message']}")

            return results

    except Exception as e:
        error_msg = f"收割和重新投放流程失败: {e}"
        logger.error(error_msg)
        results['message'] = error_msg
        results['errors'].append(error_msg)
        return results

def main():
    """主函数"""
    print("🔄 收割和重新投放工具")
    print("=" * 30)
    
    while True:
        print("\n请选择操作:")
        print("1. 查看表现良好的计划")
        print("2. 收割指定计划")
        print("3. 重新投放计划")
        print("4. 收割并重新投放")
        print("5. 退出")
        
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == "1":
            days = input("查看过去几天的数据 (默认7天): ").strip()
            days = int(days) if days.isdigit() else 7
            
            min_cost = input("最小消费金额 (默认100): ").strip()
            min_cost = float(min_cost) if min_cost else 100.0
            
            campaigns = get_performance_campaigns(days, min_cost)
            
            if campaigns:
                print(f"\n📊 表现良好的计划 (过去{days}天):")
                print("-" * 80)
                print(f"{'计划ID':<15} {'名称':<25} {'消费':<10} {'转化':<8} {'CPA':<10}")
                print("-" * 80)
                
                for item in campaigns:
                    campaign = item['campaign']
                    print(f"{campaign.campaign_id_qc:<15} {campaign.name[:24]:<25} {item['cost']:<10.2f} {item['conversions']:<8} {item['cpa']:<10.2f}")
            else:
                print("未找到表现良好的计划")
                
        elif choice == "2":
            campaign_id = input("请输入要收割的计划ID: ").strip()
            if harvest_campaign(campaign_id):
                print("✅ 计划收割成功")
            else:
                print("❌ 计划收割失败")
                
        elif choice == "3":
            campaign_id = input("请输入要重新投放的计划ID: ").strip()
            creative_path = input("新素材路径 (可选，直接回车跳过): ").strip()
            creative_path = creative_path if creative_path else None
            
            if relaunch_with_new_creative(campaign_id, creative_path):
                print("✅ 重新投放成功")
            else:
                print("❌ 重新投放失败")
                
        elif choice == "4":
            campaign_id = input("请输入计划ID: ").strip()
            creative_path = input("新素材路径 (可选): ").strip()
            creative_path = creative_path if creative_path else None
            
            print("正在收割计划...")
            if harvest_campaign(campaign_id):
                print("✅ 收割成功，开始重新投放...")
                if relaunch_with_new_creative(campaign_id, creative_path):
                    print("✅ 收割和重新投放完成")
                else:
                    print("❌ 重新投放失败")
            else:
                print("❌ 收割失败")
                
        elif choice == "5":
            print("👋 再见!")
            break
            
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
