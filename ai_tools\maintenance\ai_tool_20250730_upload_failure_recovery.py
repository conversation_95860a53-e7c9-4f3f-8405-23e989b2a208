#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期维护工具
生命周期: 永久保留
创建目的: 视频上传失败恢复和监控系统
清理条件: 成为核心功能后可移至正式目录
"""

import os
import sys
import json
import time
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Dict, Optional

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import AdAccount, PlatformCreative, Principal
from qianchuan_aw.utils.file_utils import get_file_md5

class UploadFailureRecoverySystem:
    """上传失败恢复系统"""
    
    def __init__(self):
        self.failed_uploads_file = project_root / "logs" / "failed_uploads.json"
        self.recovery_log_file = project_root / "logs" / "upload_recovery.log"
        self.ensure_log_files()
    
    def ensure_log_files(self):
        """确保日志文件存在"""
        self.failed_uploads_file.parent.mkdir(exist_ok=True)
        if not self.failed_uploads_file.exists():
            self.save_failed_uploads([])
    
    def load_failed_uploads(self) -> List[Dict]:
        """加载失败上传记录"""
        try:
            with open(self.failed_uploads_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return []
    
    def save_failed_uploads(self, failed_uploads: List[Dict]):
        """保存失败上传记录"""
        with open(self.failed_uploads_file, 'w', encoding='utf-8') as f:
            json.dump(failed_uploads, f, ensure_ascii=False, indent=2, default=str)
    
    def record_upload_failure(self, video_path: str, advertiser_id: int, error_message: str, retry_count: int = 0):
        """记录上传失败"""
        failed_uploads = self.load_failed_uploads()
        
        # 检查是否已存在
        file_hash = get_file_md5(video_path) if os.path.exists(video_path) else None
        existing_index = None
        
        for i, record in enumerate(failed_uploads):
            if record.get('video_path') == video_path and record.get('advertiser_id') == advertiser_id:
                existing_index = i
                break
        
        failure_record = {
            'video_path': video_path,
            'filename': os.path.basename(video_path),
            'advertiser_id': advertiser_id,
            'file_hash': file_hash,
            'error_message': error_message,
            'retry_count': retry_count,
            'first_failed_at': datetime.now().isoformat(),
            'last_retry_at': datetime.now().isoformat(),
            'status': 'failed',
            'recovery_attempts': 0
        }
        
        if existing_index is not None:
            # 更新现有记录
            existing = failed_uploads[existing_index]
            failure_record['first_failed_at'] = existing.get('first_failed_at', failure_record['first_failed_at'])
            failure_record['recovery_attempts'] = existing.get('recovery_attempts', 0)
            failed_uploads[existing_index] = failure_record
        else:
            # 添加新记录
            failed_uploads.append(failure_record)
        
        self.save_failed_uploads(failed_uploads)
        logger.error(f"📝 已记录上传失败: {os.path.basename(video_path)} - {error_message}")
    
    def mark_upload_success(self, video_path: str, advertiser_id: int, platform_creative_id: int):
        """标记上传成功"""
        failed_uploads = self.load_failed_uploads()
        updated = False
        
        for record in failed_uploads:
            if record.get('video_path') == video_path and record.get('advertiser_id') == advertiser_id:
                record['status'] = 'recovered'
                record['recovered_at'] = datetime.now().isoformat()
                record['platform_creative_id'] = platform_creative_id
                updated = True
                logger.success(f"✅ 标记恢复成功: {os.path.basename(video_path)}")
                break
        
        if updated:
            self.save_failed_uploads(failed_uploads)
    
    def get_pending_recoveries(self, max_age_hours: int = 24) -> List[Dict]:
        """获取待恢复的失败记录"""
        failed_uploads = self.load_failed_uploads()
        pending = []
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        for record in failed_uploads:
            if record.get('status') == 'failed':
                first_failed = datetime.fromisoformat(record.get('first_failed_at', ''))
                if first_failed > cutoff_time:
                    pending.append(record)
        
        return pending
    
    def attempt_recovery(self, record: Dict) -> bool:
        """尝试恢复单个失败的上传"""
        video_path = record['video_path']
        advertiser_id = record['advertiser_id']
        
        logger.info(f"🔄 尝试恢复上传: {os.path.basename(video_path)}")
        
        # 检查文件是否存在
        if not os.path.exists(video_path):
            logger.warning(f"⚠️ 文件不存在，跳过恢复: {video_path}")
            return False
        
        # 检查是否已在数据库中
        try:
            file_hash = get_file_md5(video_path)
            with database_session() as db:
                existing = db.query(PlatformCreative).filter(
                    PlatformCreative.file_hash == file_hash
                ).first()
                
                if existing and existing.platform_creative_id:
                    logger.info(f"✅ 文件已存在于数据库: {existing.platform_creative_id}")
                    self.mark_upload_success(video_path, advertiser_id, existing.platform_creative_id)
                    return True
        except Exception as e:
            logger.error(f"检查数据库失败: {e}")
        
        # 尝试重新上传
        try:
            # 这里应该调用实际的上传函数
            # 为了避免复杂的依赖，这里只是模拟
            logger.info(f"📤 重新上传: {os.path.basename(video_path)}")
            
            # 更新恢复尝试次数
            failed_uploads = self.load_failed_uploads()
            for r in failed_uploads:
                if r.get('video_path') == video_path and r.get('advertiser_id') == advertiser_id:
                    r['recovery_attempts'] = r.get('recovery_attempts', 0) + 1
                    r['last_retry_at'] = datetime.now().isoformat()
                    break
            self.save_failed_uploads(failed_uploads)
            
            # 实际的上传逻辑应该在这里
            # result = upload_single_video(video_path, advertiser_id)
            # if result:
            #     self.mark_upload_success(video_path, advertiser_id, result)
            #     return True
            
            return False
            
        except Exception as e:
            logger.error(f"恢复上传失败: {e}")
            return False
    
    def run_recovery_cycle(self):
        """运行恢复周期"""
        logger.info("🔄 开始上传失败恢复周期...")
        
        pending_recoveries = self.get_pending_recoveries(max_age_hours=24)
        
        if not pending_recoveries:
            logger.info("✅ 没有待恢复的失败上传")
            return
        
        logger.info(f"📋 发现 {len(pending_recoveries)} 个待恢复的失败上传")
        
        success_count = 0
        for record in pending_recoveries:
            try:
                if self.attempt_recovery(record):
                    success_count += 1
                time.sleep(2)  # 避免频繁请求
            except Exception as e:
                logger.error(f"恢复过程异常: {e}")
        
        logger.info(f"🎯 恢复周期完成: 成功 {success_count}/{len(pending_recoveries)}")
    
    def generate_failure_report(self) -> Dict:
        """生成失败报告"""
        failed_uploads = self.load_failed_uploads()
        
        total_failures = len(failed_uploads)
        active_failures = len([r for r in failed_uploads if r.get('status') == 'failed'])
        recovered_count = len([r for r in failed_uploads if r.get('status') == 'recovered'])
        
        # 按错误类型分组
        error_types = {}
        for record in failed_uploads:
            error_msg = record.get('error_message', 'Unknown')
            error_types[error_msg] = error_types.get(error_msg, 0) + 1
        
        # 按时间分组（最近24小时）
        recent_failures = []
        cutoff_time = datetime.now() - timedelta(hours=24)
        
        for record in failed_uploads:
            try:
                failed_time = datetime.fromisoformat(record.get('first_failed_at', ''))
                if failed_time > cutoff_time:
                    recent_failures.append(record)
            except:
                continue
        
        report = {
            'summary': {
                'total_failures': total_failures,
                'active_failures': active_failures,
                'recovered_count': recovered_count,
                'recent_failures_24h': len(recent_failures),
                'recovery_rate': round(recovered_count / total_failures * 100, 2) if total_failures > 0 else 0
            },
            'error_types': error_types,
            'recent_failures': recent_failures[:10],  # 最近10个
            'generated_at': datetime.now().isoformat()
        }
        
        return report
    
    def print_failure_report(self):
        """打印失败报告"""
        report = self.generate_failure_report()
        
        logger.info("📊 上传失败统计报告")
        logger.info("=" * 50)
        
        summary = report['summary']
        logger.info(f"总失败数: {summary['total_failures']}")
        logger.info(f"活跃失败: {summary['active_failures']}")
        logger.info(f"已恢复数: {summary['recovered_count']}")
        logger.info(f"最近24h失败: {summary['recent_failures_24h']}")
        logger.info(f"恢复成功率: {summary['recovery_rate']}%")
        
        if report['error_types']:
            logger.info("\n📋 错误类型分布:")
            for error_type, count in report['error_types'].items():
                logger.info(f"  {error_type}: {count} 次")
        
        if report['recent_failures']:
            logger.info(f"\n⚠️ 最近失败记录 (显示前10个):")
            for i, failure in enumerate(report['recent_failures'], 1):
                filename = failure.get('filename', 'Unknown')
                error_msg = failure.get('error_message', 'Unknown')
                failed_at = failure.get('first_failed_at', 'Unknown')
                logger.info(f"  {i}. {filename} - {error_msg} ({failed_at})")

def integrate_with_upload_function():
    """集成到现有上传函数的示例代码"""
    integration_code = '''
# 在 tools/manual_launch.py 的 upload_and_register_task 函数中添加:

from ai_tools.maintenance.ai_tool_20250730_upload_failure_recovery import UploadFailureRecoverySystem

def upload_and_register_task(client, principal_id, account_id, video_path):
    recovery_system = UploadFailureRecoverySystem()
    
    try:
        # 现有的上传逻辑...
        result = actual_upload_logic()
        
        if result:
            # 上传成功，标记恢复
            recovery_system.mark_upload_success(video_path, account_id, result)
            return result
        else:
            # 上传失败，记录失败
            recovery_system.record_upload_failure(
                video_path, account_id, "上传返回空结果", retry_count=0
            )
            return None
            
    except Exception as e:
        # 异常失败，记录失败
        recovery_system.record_upload_failure(
            video_path, account_id, str(e), retry_count=0
        )
        raise e
'''
    
    logger.info("💡 集成代码示例:")
    logger.info(integration_code)

def main():
    """主函数"""
    logger.info("🔧 上传失败恢复系统")
    logger.info("=" * 60)
    
    recovery_system = UploadFailureRecoverySystem()
    
    # 生成并显示报告
    recovery_system.print_failure_report()
    
    # 运行恢复周期
    recovery_system.run_recovery_cycle()
    
    # 显示集成建议
    integrate_with_upload_function()
    
    logger.info("\n" + "=" * 60)
    logger.info("🎯 系统运行完成！")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
